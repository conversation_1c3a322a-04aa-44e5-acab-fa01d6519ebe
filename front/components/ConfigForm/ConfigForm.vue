<template>
  <view class="config-form">
    <view class="form-item">
      <text class="label">物理高度：</text>
      <input v-model="localForm.physicalHeight" placeholder="请输入物理高度(mm)" type="number" @input="onFormChange"/>
    </view>
    <view class="form-item">
      <text class="label">物理宽度：</text>
      <input v-model="localForm.physicalWidth" placeholder="请输入物理宽度(mm)" type="number" @input="onFormChange"/>
    </view>
    <view class="form-item">
      <text class="label">偏移X：</text>
      <input v-model="localForm.offsetX" placeholder="请输入X轴偏移量(mm)" type="number" @input="onFormChange"/>
    </view>
    <view class="form-item">
      <text class="label">偏移Y：</text>
      <input v-model="localForm.offsetY" placeholder="请输入Y轴偏移量(mm)" type="number" @input="onFormChange"/>
    </view>
    <view class="form-item">
      <text class="label">X轴最大移动距离：</text>
      <input v-model="localForm.maxMoveX" placeholder="请输入X轴最大移动距离(mm)" type="number" @input="onFormChange"/>
    </view>
    <view class="form-item">
      <text class="label">Y轴最大移动距离：</text>
      <input v-model="localForm.maxMoveY" placeholder="请输入Y轴最大移动距离(mm)" type="number" @input="onFormChange"/>
    </view>
    <view class="form-item">
      <text class="label">Y轴第一排坐标：</text>
      <input v-model="localForm.yFirstRow" placeholder="请输入Y轴第一排坐标(mm)" type="number" @input="onFormChange"/>
    </view>
    <view class="form-item">
      <text class="label">Y轴第二排坐标：</text>
      <input v-model="localForm.ySecondRow" placeholder="请输入Y轴第二排坐标(mm)" type="number" @input="onFormChange"/>
    </view>
    <view class="form-item">
      <text class="label">X坐标增量：</text>
      <input v-model="localForm.xIncrement" placeholder="请输入X坐标每次增加的步长(mm)" type="number" @input="onFormChange"/>
    </view>

    <view class="btn-group">
      <button @click="onSave" class="save-btn">保存配置</button>
      <button @click="onCancel" class="cancel-btn" v-if="showCancel">取消</button>
    </view>
  </view>
</template>

<script lang="ts">
export default {
  name: 'ConfigForm',
  props: {
    title: {
      type: String,
      default: '物理尺寸配置'
    },
    formData: {
      type: Object,
      default: () => ({
        physicalHeight: '',
        physicalWidth: '',
        offsetX: '',
        offsetY: '',
        maxMoveX: '',
        maxMoveY: '',
        yFirstRow: '',
        ySecondRow: '',
        xIncrement: ''
      })
    },
    showCancel: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localForm: {
        physicalHeight: '',
        physicalWidth: '',
        offsetX: '',
        offsetY: '',
        maxMoveX: '',
        maxMoveY: '',
        yFirstRow: '',
        ySecondRow: '',
        xIncrement: ''
      }
    }
  },
  watch: {
    formData: {
      handler(newVal) {
        this.localForm = { ...newVal };
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    onFormChange() {
      this.$emit('form-change', this.localForm);
    },
    onSave() {
      // 验证输入
      if (!this.localForm.physicalHeight || !this.localForm.physicalWidth) {
        uni.showToast({title: '请填写完整的物理尺寸', icon: 'none'});
        return;
      }
      
      this.$emit('save', this.localForm);
    },
    onCancel() {
      this.$emit('cancel');
    }
  }
}
</script>

<style lang="scss" scoped>
.config-form {
  padding: 32rpx;
}
.form-item {
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
}

.label {
  width: 200rpx;
  font-size: 30rpx;
  color: #333;
}

input {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 28rpx;
}

.btn-group {
  display: flex;
  gap: 20rpx;
}

.save-btn {
  flex: 1;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 0;
  font-size: 32rpx;
}

.cancel-btn {
  flex: 1;
  background: #f5f5f5;
  color: #333;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 0;
  font-size: 32rpx;
}
</style>