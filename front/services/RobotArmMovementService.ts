/**
 * 机械臂移动控制服务
 * 负责管理机械臂的移动逻辑、位置计算和路径规划
 */

import {ConfigService} from "./ConfigService";
import {CountService} from "./CountService";
import {mobileLog} from "../utils/funs";

export class RobotArmMovementService {

    xIncrement: number
    yFirstRow: number
    ySecondRow: number
    maxMoveX: number

    // 当前位置状态
    currX: number = 0 // 当前X轴位置
    currY: number


    // 移动状态
    direction: number = 1   // 移动方向：1为向右，-1为向左
    currentRow: number = 1 // 当前行：1为第一行，2为第二行


    constructor() {


        // 移动配置参数
        this.xIncrement = ConfigService.getXIncrement();  // X轴增量
        this.yFirstRow = ConfigService.getYFirstRow(); // 第一行Y轴坐标
        this.ySecondRow = ConfigService.getYSecondRow();   // 第二行Y轴坐标

        const maxMoveX = ConfigService.getMaxMoveX();  // 最大X轴移动距离
        // 最大X轴移动距离需要减去最后一屏拍照位置的偏移
        this.maxMoveX = maxMoveX - (1280 / CountService.getPxWidth() - 10);


        this.currY = this.yFirstRow; // 当前Y轴位置


        mobileLog(`机械臂移动服务初始化完成: X增量=${this.xIncrement}, Y第一行=${this.yFirstRow}, Y第二行=${this.ySecondRow}, 最大X=${this.maxMoveX}`, 'info');
    }


    /**
     * 计算下一个移动位置
     * 实现蛇形移动模式：第一行从左到右，第二行从右到左
     */
    calculateNextPosition() {
        // 根据方向移动X轴
        this.currX += this.xIncrement * this.direction;

        // 检查是否到达边界
        if (this.direction === 1 && this.currX >= this.maxMoveX) {
            // 向右移动到达最大位置，切换到下一行
            this.currX = this.maxMoveX;
            mobileLog('到达右边界，准备切换到下一行', 'info');
            this.switchToNextRow();
        } else if (this.direction === -1 && this.currX <= 0) {
            // 向左移动到达最小位置，切换到下一行
            this.currX = 0;
            mobileLog('到达左边界，准备切换到下一行', 'info');
            this.switchToNextRow();
        }
    }

    /**
     * 切换到下一行
     * 实现蛇形移动：第一行→第二行→第一行...
     */
    switchToNextRow() {
        if (this.currentRow === 1) {
            // 从第一行切换到第二行
            this.currentRow = 2;
            this.currY = this.ySecondRow;
            this.direction = -1; // 第二行从右到左
            mobileLog('切换到第二行，方向：从右到左', 'info');
        } else {
            // 从第二行切换到第一行
            this.currentRow = 1;
            this.currY = this.yFirstRow;
            this.direction = 1; // 第一行从左到右
            mobileLog('切换到第一行，方向：从左到右', 'info');
        }
    }


}
