<template>
  <set-device-id @selected="selectedDeviceId">
    <view class="container">

      <view class="tab-bar">
        <!-- 连接状态 -->
        <ConnectionStatus :websocketService="websocketService"/>

        <view class="btn" @click="navigateToPage('ca')">证书</view>
        <view class="btn" @click="navigateToPage('/pages/index/config')">配置</view>
        <view class="btn" @click="navigateToPage('/pages/calibration/calibration-target')">校准标记</view>
      </view>


      <!-- 相机预览 -->
      <view class="camera-section" v-if="websocketService" >
        <TakePhoto ref="takePhoto" :websocketService="websocketService" :device-id="deviceId"/>
      </view>

      <!-- 操作提示 -->
      <view class="tips">
        <text>提示：保持手机连接，等待PC端发送拍照指令。</text>
      </view>

      <!-- 调试信息区域 -->
      <DebugSection ref="debugSection"/>
    </view>
  </set-device-id>
</template>

<script lang="ts">
import TakePhoto from "../../components/TakePhoto/TakePhoto";
import SetDeviceId from "../../components/SetDeviceId/SetDeviceId";
import {WebsocketService} from "../../services/WebsocketService";
import {ConfigWebsocketService} from "../../services/websocket/ConfigWebsocketService";
import ConnectionStatus from "../../components/ConnectionStatus/ConnectionStatus";
import {AvailableMobileSocket} from "../../services/websocket/AvailableMobileSocket";
import {TaskStatusSocket} from "../../services/websocket/TaskStatusSocket";
import {RobotArmSocket} from "../../services/websocket/RobotArmSocket";
import DebugSection from "../../components/DebugSection/DebugSection";
import {mobileLog} from "../../utils/funs";


export default {
  name: "CameraMobile",
  components: {DebugSection, ConnectionStatus, TakePhoto, SetDeviceId},
  data() {
    return {

      websocketService: WebsocketService|null ,

      robotArmService: null,
      // 设备状态
      deviceId: '',



    };
  },

  beforeDestroy() {
    this.cleanup();
  },
  methods: {
    navigateToPage(path) {
      if (path === 'ca') {
        window.location.href = "/static/rootCA.pem"
      } else {
        uni.navigateTo({
          url: path
        });
      }
    },
    selectedDeviceId(deviceId) {
      this.deviceId = deviceId;
      mobileLog(`设备ID已选择: ${deviceId}`, 'info');
      this.initWebSocket();
    },
    initWebSocket() {
      // 初始化WebSocket服务
      mobileLog('正在初始化WebSocket服务...', 'info');
      this.websocketService = new WebsocketService();
      this.websocketService.init(this.deviceId)
      mobileLog(`WebSocket服务初始化完成，设备ID: ${this.deviceId}`, 'success');

      // 监听错误事件
      this.websocketService.on('error', (data) => {
        console.error('移动端WebSocket错误:', data);
        const errorMsg = data.message || data.error || '未知错误';
        mobileLog(`WebSocket错误: ${errorMsg}`, 'error');
        if (data.message || data.error) {
          uni.showModal({title: data.message || data.error, icon: 'error'});
        }
      });


      // 监听获取在线设备
      AvailableMobileSocket.onGetAvailableMobiles(this.websocketService)


      // 监听通用配置服务
      ConfigWebsocketService.onGetConfig(this.websocketService);
      ConfigWebsocketService.onSetConfig(this.websocketService);


      // 通知任务事件
      TaskStatusSocket.setupEventListeners(this.websocketService)

      // 通知机械臂行动事件
      RobotArmSocket.setupEventListeners(this.websocketService)
    },


    cleanup() {
      // 关闭WebSocket服务
      this.websocketService.close();
    },


  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
}

.tab-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .connection-status {
    margin-bottom: 0;
  }
}

.camera-section {
  margin: 30rpx 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}


.camera-controls {
  text-align: center;
}

.waiting-command {
  font-size: 26rpx;
  color: #FF9800;
}


.tips {
  background-color: #FFF3CD;
  padding: 20rpx;
  border-radius: 10rpx;
  margin: 30rpx 0;
  font-size: 24rpx;
  color: #856404;
  line-height: 1.5;
}
</style>
