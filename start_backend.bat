@echo off
echo 启动AI服务后端 (包含YOLO检测、机械臂控制、WebSocket通信、OCR文字识别)...
echo.

cd /d "%~dp0backed"

echo 检查Python环境...
python --version
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python
    pause
    exit /b 1
)

echo.
echo 安装依赖包...
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple

echo.
echo 初始化OCR模型 (首次运行可能需要下载模型)...
python -c "from paddleocr import PaddleOCR; ocr = PaddleOCR(use_angle_cls=True, lang='ch', use_gpu=False, show_log=False); print('OCR模型初始化完成')"

echo.
echo 启动AI服务...
echo 服务地址: https://localhost:8000
echo WebSocket地址: wss://localhost:8000/ws
echo OCR API: https://localhost:8000/ocr/
echo API文档: https://localhost:8000/docs
echo.
echo 按 Ctrl+C 停止服务
echo.

uvicorn main:app --host 0.0.0.0 --port 8000 --reload --ssl-keyfile ../front/ssl/localhost+3-key.pem --ssl-certfile ../front/ssl/localhost+3.pem

pause
