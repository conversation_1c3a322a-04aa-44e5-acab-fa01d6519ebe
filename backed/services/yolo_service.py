"""
YOLO识别服务
封装YOLO模型的识别逻辑，提供标准化的接口
"""

import os
import uuid
import numpy as np
import logging
from typing import List, Dict, Any, Tuple
from fastapi import HTTPException
from ultralytics import YOLO
from PIL import Image
from io import BytesIO
from .image_utils import CoordinateConverter

logger = logging.getLogger(__name__)

class YOLOService:
    """YOLO识别服务"""
    
    def __init__(self, model_path: str = None, save_dir: str = None):
        # 设置模型路径
        if model_path is None:
            model_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'best.pt'))
        
        # 设置保存目录
        if save_dir is None:
            save_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'yolo_detected_images'))
        
        self.model_path = model_path
        self.save_dir = save_dir
        
        # 确保模型路径存在
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"YOLO model not found at {self.model_path}")
        
        # 确保保存目录存在
        os.makedirs(self.save_dir, exist_ok=True)
        
        # 加载模型
        self.model = YOLO(self.model_path)
        logger.info(f"YOLO模型加载成功: {self.model_path}")
    
    def detect_objects(
        self, 
        image_bytes: bytes, 
        original_size: Tuple[int, int] = (720, 1280),
        convert_to_ocr_coords: bool = True
    ) -> List[Dict[str, Any]]:
        """
        检测图像中的对象
        
        Args:
            image_bytes: 图像字节数据
            original_size: 原始图像尺寸 (width, height)
            convert_to_ocr_coords: 是否转换为OCR坐标系
            
        Returns:
            检测结果列表
        """
        try:
            # 从内存字节流中打开图片
            image = Image.open(BytesIO(image_bytes)).convert("RGB")
            
            # 使用YOLO模型进行推理
            results = self.model(image, verbose=False)
            result = results[0]
            boxes = result.boxes
            names = result.names
            
            output = []
            if len(boxes) > 0:
                xyxy_boxes = boxes.xyxy.cpu().numpy()
                labels_indices = boxes.cls.cpu().numpy().astype(int)
                confs = boxes.conf.cpu().numpy()

                for i in range(len(xyxy_boxes)):
                    box = xyxy_boxes[i]
                    label_index = labels_indices[i]
                    conf = float(confs[i])
                    category = names.get(label_index, "Unknown")
                    x_min, y_min, x_max, y_max = [float(coord) for coord in box]
                    
                    # 原始YOLO边界框
                    yolo_bbox = [x_min, y_min, x_max, y_max]
                    
                    if convert_to_ocr_coords:
                        # 转换为OCR坐标系 (旋转90度后的坐标系)
                        rotated_size = (original_size[1], original_size[0])  # 旋转后尺寸
                        bounding_box = CoordinateConverter.yolo_to_ocr_coordinates(
                            yolo_bbox, original_size, rotated_size
                        )
                    else:
                        # 保持原始YOLO坐标
                        bounding_box = {
                            "rectangle": {
                                "x_min": int(x_min),
                                "y_min": int(y_min),
                                "x_max": int(x_max),
                                "y_max": int(y_max),
                                "width": int(x_max - x_min),
                                "height": int(y_max - y_min),
                                "center_x": int((x_max + x_min) / 2),
                                "center_y": int((y_max + y_min) / 2)
                            },
                            "opencv_rect": [int(x_min), int(y_min), int(x_max - x_min), int(y_max - y_min)]
                        }
                    
                    output.append({
                        "name": category,
                        "type": "yolo",
                        "confidence": conf,
                        "bounding_box": bounding_box,
                        "class_id": int(label_index)
                    })
            
            return output
            
        except Exception as e:
            logger.error(f"YOLO检测失败: {e}")
            raise HTTPException(status_code=500, detail=f"YOLO检测失败: {str(e)}")
    
    def save_image_if_needed(self, image_bytes: bytes, filename: str, detection_count: int) -> bool:
        """
        根据检测结果决定是否保存图像
        只有当检测到的目标数量 <= 1 时才保存
        
        Args:
            image_bytes: 图像字节数据
            filename: 原始文件名
            detection_count: 检测到的目标数量
            
        Returns:
            是否保存了图像
        """
        if detection_count <= 1:
            try:
                # 使用UUID确保文件名唯一，避免覆盖
                file_extension = os.path.splitext(filename)[1] if filename and '.' in filename else '.jpg'
                unique_filename = f"{uuid.uuid4()}{file_extension}"
                save_path = os.path.join(self.save_dir, unique_filename)
                
                with open(save_path, "wb") as f:
                    f.write(image_bytes)
                
                logger.info(f"图像已保存: {save_path}")
                return True
            except Exception as e:
                logger.error(f"保存图片失败: {e}")
                return False
        return False
    
    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "service": "YOLO Detection Service",
            "version": "1.0.0",
            "model_path": self.model_path,
            "save_directory": self.save_dir,
            "features": [
                "目标检测",
                "多类别识别",
                "置信度评估",
                "边界框定位",
                "坐标系转换"
            ],
            "coordinate_systems": [
                "原始YOLO坐标",
                "OCR坐标系 (旋转90度)"
            ]
        }

# 全局YOLO服务实例
try:
    yolo_service = YOLOService()
except Exception as e:
    logger.error(f"YOLO服务初始化失败: {e}")
    yolo_service = None
