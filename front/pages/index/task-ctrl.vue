<template>
  <view class="task-ctrl-container">

    <view class="connect-status-warp">
      <!-- 连接状态 -->
      <ConnectionStatus class="connect-status" :websocketService="websocketService"/>

      <view class="btn" @click="navigateToPage('/pages/jz/size-pc')">计算尺寸</view>
      <view class="btn" @click="navigateToPage('/pages/calibration/calibration-pc')">自动校准</view>

    </view>

    <view class="devices">
      <view class="device-item" v-for="deviceId in availableMobiles" :key="deviceId">
        <view class="device-actions">
          <view class="name">设备 {{ deviceId }}</view>
          <view class="device-btns">
            <view class="btn config-btn" @click="openConfig(deviceId)">配置</view>
            <view class="btn take-photo" @click="takePhone(deviceId)">拍照</view>
            <view class="btn task-btn" v-if="devicePortHandle[deviceId]"
                  :class="{'stop-task': deviceTaskStatus[deviceId]}" @click="toggleTask(deviceId)">
              {{ deviceTaskStatus[deviceId] ? '中止任务' : '开始任务' }}
            </view>
          </view>
        </view>


        <RobotDebug :websocket-service="websocketService" :device-id="deviceId"/>
        <view class="image-warp" v-if="devicePhoneData[deviceId]">
          <image class="image" mode="widthFix" :src="devicePhoneData[deviceId]"></image>
        </view>
      </view>
    </view>

    <!-- 配置弹窗 -->
    <view class="config-modal" v-if="showConfigModal" @click="closeConfigModal">
      <view class="config-modal-content" @click.stop>
        <view class="config-modal-header">
          <text class="config-modal-title">设备 {{ currentConfigDeviceId }} 配置</text>
          <view class="config-modal-close" @click="closeConfigModal">×</view>
        </view>
        <ConfigForm
            :title="`设备 ${currentConfigDeviceId} 配置`"
            :form-data="configForm"
            :show-cancel="true"
            @save="saveDeviceConfig"
            @cancel="closeConfigModal"
            @form-change="onConfigFormChange"
        />
      </view>
    </view>

  </view>
</template>

<script lang="ts">
import {WebsocketService} from "../../services/WebsocketService";
import {DeviceManagementService} from "../../services/DeviceManagementService";
import {TakePhoneSocket} from "../../services/websocket/TakePhoneSocket";
import {ConfigWebsocketService} from "../../services/websocket/ConfigWebsocketService";
import ConfigForm from '../../components/ConfigForm/ConfigForm.vue';
import {TaskStatusSocket} from "../../services/websocket/TaskStatusSocket";
import ConnectionStatus from "../../components/ConnectionStatus/ConnectionStatus.vue";
import RobotDebug from "../../components/RobotDebug/RobotDebug.vue";


export default {
  components: {
    RobotDebug,
    ConnectionStatus,
    ConfigForm
  },
  data() {
    return {
      websocketService: null,
      deviceManagementService: null,
      deviceId: 'task_ctrl',

      // 在线设备列表
      availableMobiles: [],

      // 每个设备的最新照片数据
      devicePhoneData: {},

      // 每个设备的任务运行状态
      deviceTaskStatus: {},

      // 每个设备的端口句柄
      devicePortHandle: {},

      // 配置弹窗相关
      showConfigModal: false,
      currentConfigDeviceId: '',
      configForm: {
        physicalHeight: '',
        physicalWidth: '',
        offsetX: '',
        offsetY: '',
        maxMoveX: '',
        maxMoveY: '',
        yFirstRow: '',
        ySecondRow: '',
        xIncrement: ''
      }
    }
  },
  onLoad() {
    this.init();

    uni.$on('robotStatusChange', res => {
      if (res.handle) {
        this.devicePortHandle[res.deviceId] = res.handle;
      }
    })

  },
  methods: {
    navigateToPage(path) {
      uni.navigateTo({
        url: path
      });
    },
    init() {
      this.websocketService = new WebsocketService();
      this.websocketService.init(this.deviceId);
      this.setupEventListeners();
      this.initDeviceManagement();
    },

    setupEventListeners() {
      // 接收手机的最新照片
      TakePhoneSocket.receivePhoneByMobile(this.websocketService, (photoData, deviceId) => {
        console.log(photoData)
        // 直接使用 Blob 创建对象 URL
        this.devicePhoneData[deviceId] = URL.createObjectURL(photoData);
      })


      this.websocketService.on('connected', () => {

      })

    },
    initDeviceManagement() {
      this.deviceManagementService = new DeviceManagementService(this.websocketService);

      // 监听设备列表变化
      this.deviceManagementService.onDeviceListChange((devices) => {
        this.availableMobiles = devices;


        for (let i = 0; i < this.availableMobiles.length; i++) {
          const deviceId = this.availableMobiles[i];
          // 获取任务状态
          this.getTaskStatus(deviceId);
        }
      });
    },
    takePhone(deviceId) {
      TakePhoneSocket.sendTakePhone(this.websocketService, deviceId);
    },

    toggleTask(deviceId) {
      if (this.deviceTaskStatus[deviceId]) {
        // 当前任务正在运行，执行中止任务
        TaskStatusSocket.notifyStopTask(this.websocketService, deviceId, () => {
          console.log('移动端任务已经中止')
          this.deviceTaskStatus[deviceId] = false;
        });
      } else {
        // 当前任务未运行，执行开始任务
        TaskStatusSocket.notifyStartTask(this.websocketService, deviceId, () => {
          this.deviceTaskStatus[deviceId] = true;
        });
      }
    },
    getTaskStatus(deviceId) {
      TaskStatusSocket.getTaskStatus(this.websocketService, deviceId, (retDeviceId, status) => {
        this.deviceTaskStatus[retDeviceId] = status;
      })
    },


    // 配置相关方法
    openConfig(deviceId) {
      this.currentConfigDeviceId = deviceId;
      this.loadDeviceConfig(deviceId);
      this.showConfigModal = true;
    },

    closeConfigModal() {
      this.showConfigModal = false;
      this.currentConfigDeviceId = '';
    },

    loadDeviceConfig(deviceId) {
      // 通过 WebSocket 获取指定设备的配置
      ConfigWebsocketService.getConfig(deviceId, this.websocketService, (config) => {
        this.configForm = {...config};
      });
    },

    saveDeviceConfig(formData) {
      // 通过 WebSocket 设置指定设备的配置
      ConfigWebsocketService.setConfig(this.currentConfigDeviceId, formData, this.websocketService);
      uni.showToast({title: '配置已发送', icon: 'success'});
      this.closeConfigModal();
    },

    onConfigFormChange(formData) {
      this.configForm = {...formData};
    }

  }
}
</script>

<style lang="scss" scoped>

.task-ctrl-container {
  padding: 24rpx;
}

.connect-status-warp {
  display: flex;
  align-items: center;
  position: fixed;
  height: 44px;
  padding-right: 12px;
  right: 0;
  top: 0;
  gap: 10px;
  z-index: 9999;

  .connect-status {
    margin-bottom: 0;
    padding: 3px 12px;
  }
}

.devices {
  display: flex;
  padding-top: 20px;

  .device-item {
    position: relative;
    width: 32%;
    border: 1px solid #ececec;
    padding: 20px 12px 12px;

    .device-actions {
      position: absolute;
      top: -15px;
      left: 12px;
      width: 94%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;

      .name {
        background-color: #ffffff;
        font-weight: bold;
        padding: 0 8px;
      }

      .device-btns {
        display: flex;
        gap: 10px;
      }
    }

    .image-warp {
      width: 100%;

      .image {
        width: 100%;
      }
    }

  }
}

// 配置按钮样式
.config-btn {
  background-color: #28a745 !important;
  color: white !important;
}

// 任务按钮样式
.task-btn {
  background-color: #007bff !important;
  color: white !important;

  &.stop-task {
    background-color: #dc3545 !important;
  }
}

// 配置弹窗样式
.config-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.config-modal-content {
  background-color: white;
  border-radius: 12rpx;
  width: 90%;
  max-width: 800rpx;
  max-height: 80%;
  overflow-y: auto;
}

.config-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1px solid #eee;
}

.config-modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.config-modal-close {
  font-size: 48rpx;
  color: #999;
  cursor: pointer;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

</style>
