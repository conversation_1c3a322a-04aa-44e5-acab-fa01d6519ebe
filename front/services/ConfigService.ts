/**
 * 配置服务 - 统一管理物理尺寸配置数据
 * 提供统一的get/set方法来操作配置数据
 */
import {CacheMgr} from "../utils/CacheMgr";

const PHYSICAL_HEIGHT = 'physicalHeight',
    PHYSICAL_WIDTH = 'physicalWidth',
    OFFSET_X = 'offsetX',
    OFFSET_Y = 'offsetY',
    MAX_MOVE_X = 'maxMoveX',
    MAX_MOVE_Y = 'maxMoveY',
    DEVICE_ID = 'deviceId',
    Y_FIRST_ROW = 'yFirstRow',
    Y_SECOND_ROW = 'ySecondRow',
    X_INCREMENT = 'xIncrement';


export class ConfigService {


    /**
     * 获取设备ID
     * @returns {string} 设备ID
     */
    static getDeviceId(): string {
        let deviceId = CacheMgr.get(DEVICE_ID);
        return deviceId ? deviceId : '';
    }

    /**
     * 设置设备ID
     * @param {string} value 设备ID
     */
    static setDeviceId(value: string) {
        CacheMgr.set(DEVICE_ID, value);
    }


    /**
     * 获取物理宽度
     * @returns {number} 物理宽度值
     */
    static getPhysicalWidth(): number {
        let num = CacheMgr.get(PHYSICAL_WIDTH);
        return num ? Number(Number(num).toFixed(2)) : 0;
    }

    /**
     * 设置物理宽度
     * @param {number} value 物理宽度值
     */
    static setPhysicalWidth(value: number) {
        CacheMgr.set(PHYSICAL_WIDTH, value);
    }

    /**
     * 获取物理高度
     * @returns {number} 物理宽高值
     */
    static getPhysicalHeight(): number {
        let num = CacheMgr.get(PHYSICAL_HEIGHT);
        return num ? Number(Number(num).toFixed(2)) : 0;
    }

    /**
     * 设置物理高度
     * @param {number} value 物理宽高值
     */
    static setPhysicalHeight(value: number) {
        CacheMgr.set(PHYSICAL_HEIGHT, value);
    }


    /**
     * 获取X轴偏移量
     * @returns {number} X轴偏移量
     */
    static getOffsetX(): number {
        let num = CacheMgr.get(OFFSET_X);
        return num ? Number(Number(num).toFixed(2)) : 0;
    }

    /**
     * 设置X轴偏移量
     * @param {number} value X轴偏移量
     */
    static setOffsetX(value: number) {
        CacheMgr.set(OFFSET_X, value);
    }

    /**
     * 获取Y轴偏移量
     * @returns {number} Y轴偏移量
     */
    static getOffsetY(): number {
        let num = CacheMgr.get(OFFSET_Y);
        return num ? Number(Number(num).toFixed(2)) : 0;
    }

    /**
     * 设置Y轴偏移量
     * @param {number} value Y轴偏移量
     */
    static setOffsetY(value: number) {
        CacheMgr.set(OFFSET_Y, value);
    }

    /**
     * 获取X轴最大移动距离
     * @returns {number} X轴最大移动距离
     */
    static getMaxMoveX(): number {
        let num = CacheMgr.get(MAX_MOVE_X);
        return num ? Number(Number(num).toFixed(2)) : 855;
    }

    /**
     * 设置X轴最大移动距离
     * @param {number} value X轴最大移动距离
     */
    static setMaxMoveX(value: number) {
        CacheMgr.set(MAX_MOVE_X, value);
    }

    /**
     * 获取Y轴最大移动距离
     * @returns {number} Y轴最大移动距离
     */
    static getMaxMoveY(): number {
        let num = CacheMgr.get(MAX_MOVE_Y);
        return num ? Number(Number(num).toFixed(2)) : 145;
    }

    /**
     * 设置Y轴最大移动距离
     * @param {number} value Y轴最大移动距离
     */
    static setMaxMoveY(value: number) {
        CacheMgr.set(MAX_MOVE_Y, value);
    }

    /**
     * 获取Y轴第一排坐标
     * @returns {number} Y轴第一排坐标
     */
    static getYFirstRow(): number {
        let num = CacheMgr.get(Y_FIRST_ROW);
        return num ? Number(Number(num).toFixed(2)) : 60;
    }

    /**
     * 设置Y轴第一排坐标
     * @param {number} value Y轴第一排坐标
     */
    static setYFirstRow(value: number) {
        CacheMgr.set(Y_FIRST_ROW, value);
    }

    /**
     * 获取Y轴第二排坐标
     * @returns {number} Y轴第二排坐标
     */
    static getYSecondRow(): number {
        let num = CacheMgr.get(Y_SECOND_ROW);
        return num ? Number(Number(num).toFixed(2)) : 260;
    }

    /**
     * 设置Y轴第二排坐标
     * @param {number} value Y轴第二排坐标
     */
    static setYSecondRow(value: number) {
        CacheMgr.set(Y_SECOND_ROW, value);
    }

    /**
     * 获取X坐标每次增加的步长
     * @returns {number} X坐标增加步长
     */
    static getXIncrement(): number {
        let num = CacheMgr.get(X_INCREMENT);
        return num ? Number(Number(num).toFixed(2)) : 60;
    }

    /**
     * 设置X坐标每次增加的步长
     * @param {number} value X坐标增加步长
     */
    static setXIncrement(value: number) {
        CacheMgr.set(X_INCREMENT, value);
    }


}
