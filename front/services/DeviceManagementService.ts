import {AvailableMobileSocket} from "./websocket/AvailableMobileSocket";

type DeviceListChangeCallback = (devices: string[]) => void;

export class DeviceManagementService {
  private readonly websocketService: any;
  private availableMobiles: string[] = [];
  private deviceLastOnlineTime: Record<string, number> = {};
  private getDevicesTimer:number = 0;
  private onDeviceListChangeCallback: DeviceListChangeCallback | null = null;
  private timeoutDuration: number = 15000; // 15秒

  constructor(websocketService: any) {
    this.websocketService = websocketService;
    
    // 在线设备列表
    this.availableMobiles = [];
    
    // 记录每个设备的最后在线时间
    this.deviceLastOnlineTime = {};
    

    // 定时获取在线设备的定时器
    this.getDevicesTimer = 0;
    
    // 设备列表变化的回调函数
    this.onDeviceListChangeCallback = null;
    
    // 超时时长（毫秒）
    this.timeoutDuration = 15000; // 15秒
    
    this.init();
  }
  
  private init(): void {
    this.setupEventListeners();
  }
  
  private setupEventListeners(): void {
    // 监听 WebSocket 连接成功
    this.websocketService.on('connected', () => {
      this.getAvailableMobilesHandle();
      this.startPeriodicTasks();
    });
    

  }
  

  
  /**
   * 获取在线设备
   */
  private getAvailableMobilesHandle(): void {
    AvailableMobileSocket.getAvailableMobiles(this.websocketService).then((deviceId:any)=>{
        // 检查设备是否已存在
        if (!this.availableMobiles.includes(deviceId)) {
            this.availableMobiles.push(deviceId);
            // 按升序排列
            this.availableMobiles.sort((a, b) => a.localeCompare(b));
            this.notifyDeviceListChange();
        }

        // 更新设备的最后在线时间
        this.deviceLastOnlineTime[deviceId] = Date.now();
    });
  }
  
  /**
   * 启动定期任务
   */
  private startPeriodicTasks(): void {
    // 定时获取在线设备
    this.getDevicesTimer = setInterval(() => {
      this.getAvailableMobilesHandle();
      this.checkDeviceTimeout();
    }, 5000);

  }
  
  /**
   * 检查设备超时
   */
  private checkDeviceTimeout(): void {
    const currentTime = Date.now();
    const originalLength = this.availableMobiles.length;
    
    // 检查每个设备是否超时
    this.availableMobiles = this.availableMobiles.filter(deviceId => {
      const lastOnlineTime = this.deviceLastOnlineTime[deviceId];
      if (lastOnlineTime && (currentTime - lastOnlineTime) > this.timeoutDuration) {
        // 设备超时，清理相关数据
        delete this.deviceLastOnlineTime[deviceId];
        return false; // 从列表中移除
      }
      return true; // 保留在列表中
    });
    
    // 如果设备列表发生变化，通知回调
    if (this.availableMobiles.length !== originalLength) {
      this.notifyDeviceListChange();
    }
  }
  
  /**
   * 设置设备列表变化的回调函数
   * @param {Function} callback 回调函数
   */
  onDeviceListChange(callback: DeviceListChangeCallback): void {
    this.onDeviceListChangeCallback = callback;
  }
  
  /**
   * 通知设备列表变化
   */
  private notifyDeviceListChange(): void {
    if (this.onDeviceListChangeCallback) {
      this.onDeviceListChangeCallback([...this.availableMobiles]);
    }
  }
  
  /**
   * 获取当前在线设备列表
   * @returns {Array} 设备列表
   */
  getAvailableDevices(): string[] {
    return [...this.availableMobiles];
  }
  
  /**
   * 获取设备的最后在线时间
   * @param {string} deviceId 设备ID
   * @returns {number|null} 最后在线时间戳
   */
  getDeviceLastOnlineTime(deviceId: string): number | null {
    return this.deviceLastOnlineTime[deviceId] || null;
  }
  
  /**
   * 设置超时时长
   * @param {number} duration 超时时长（毫秒）
   */
  setTimeoutDuration(duration: number): void {
    this.timeoutDuration = duration;
  }
  
  /**
   * 销毁服务，清理定时器
   */
  destroy(): void {
    if (this.getDevicesTimer) {
      clearInterval(this.getDevicesTimer);
      this.getDevicesTimer = 0;
    }
    
    this.availableMobiles = [];
    this.deviceLastOnlineTime = {};
    this.onDeviceListChangeCallback = null;
  }
}