import {ConfigService} from "../ConfigService";

interface Config {
    physicalHeight?: number;
    physicalWidth?: number;
    offsetX?: number;
    offsetY?: number;
    maxMoveX?: number;
    maxMoveY?: number;
    yFirstRow?: number;
    ySecondRow?: number;
    xIncrement?: number;
    deviceId?: string;
}

const setLocalConfig = (config: Config): void => {
    // 更新本地配置
    if (config.physicalHeight !== undefined) ConfigService.setPhysicalHeight(config.physicalHeight);
    if (config.physicalWidth !== undefined) ConfigService.setPhysicalWidth(config.physicalWidth);
    if (config.offsetX !== undefined) ConfigService.setOffsetX(config.offsetX);
    if (config.offsetY !== undefined) ConfigService.setOffsetY(config.offsetY);
    if (config.maxMoveX !== undefined) ConfigService.setMaxMoveX(config.maxMoveX);
    if (config.maxMoveY !== undefined) ConfigService.setMaxMoveY(config.maxMoveY);
    if (config.yFirstRow !== undefined) ConfigService.setYFirstRow(config.yFirstRow);
    if (config.ySecondRow !== undefined) ConfigService.setYSecondRow(config.ySecondRow);
    if (config.xIncrement !== undefined) ConfigService.setXIncrement(config.xIncrement);
}

/**
 * 通用配置 WebSocket 服务
 * 提供统一的配置获取和设置方法
 */
export class ConfigWebsocketService {

    /**
     * 获取指定设备的完整配置
     * @param {string} deviceId 设备ID
     * @param {Object} websocketService WebSocket服务实例
     * @param {Function} callback 回调函数，接收配置数据
     */
    static getConfig(deviceId: string, websocketService: any, callback: (config: Config) => void): void {
        websocketService.on('get-config-return', (res: any) => {
            const msg = res.json;
            if (msg.config.deviceId === deviceId) {
                setLocalConfig(msg.config)
                callback(msg.config);
            }
        });

        websocketService.send('get-config', {
            deviceId: deviceId
        });
    }

    /**
     * 监听获取配置请求（手机端使用）
     * @param {Object} websocketService WebSocket服务实例
     */
    static onGetConfig(websocketService: any): void {
        websocketService.on('get-config', (res: any) => {
            const msg = res.json;
            if (msg.deviceId === ConfigService.getDeviceId()) {
                const config: Config = {
                    physicalHeight: ConfigService.getPhysicalHeight(),
                    physicalWidth: ConfigService.getPhysicalWidth(),
                    offsetX: ConfigService.getOffsetX(),
                    offsetY: ConfigService.getOffsetY(),
                    maxMoveX: ConfigService.getMaxMoveX(),
                    maxMoveY: ConfigService.getMaxMoveY(),
                    yFirstRow: ConfigService.getYFirstRow(),
                    ySecondRow: ConfigService.getYSecondRow(),
                    xIncrement: ConfigService.getXIncrement(),
                    deviceId: ConfigService.getDeviceId(),
                };

                websocketService.send('get-config-return', {
                    config: config
                });
            }
        });
    }

    /**
     * 设置指定设备的配置
     * @param {string} deviceId 设备ID
     * @param {Object} config 配置对象
     * @param {Object} websocketService WebSocket服务实例
     */
    static setConfig(deviceId: string, config: Config, websocketService: any): void {
        websocketService.send('set-config', {
            deviceId: deviceId,
            config: config
        });
    }

    /**
     * 监听设置配置请求（手机端使用）
     * @param {Object} websocketService WebSocket服务实例
     */
    static onSetConfig(websocketService: any): void {
        websocketService.on('set-config', (res: any) => {
            const msg = res.json;
            if (msg.deviceId === ConfigService.getDeviceId()) {
                const config = msg.config;

                setLocalConfig(config)
                uni.showToast({title: '配置已更新', icon: 'success'}).then(_r => {
                });
            }
        });
    }
}