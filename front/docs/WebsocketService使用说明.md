# WebsocketService 使用说明

## 概述

优化后的 `WebsocketService` 提供了以下核心功能：

- ✅ **自动重连**：指数退避算法，智能重连
- ✅ **心跳保活**：30秒心跳，10秒超时检测
- ✅ **消息队列**：断线时消息自动入队，重连后发送
- ✅ **事件管理**：简洁的事件监听和触发机制
- ✅ **错误处理**：完善的错误处理和日志记录
- ✅ **资源清理**：自动清理定时器和事件监听器

## 基本使用

```typescript
import { WebsocketService } from './services/WebsocketService';

// 创建实例
const wsService = new WebsocketService();

// 初始化连接
try {
    await wsService.init('your-device-id');
    console.log('连接成功');
} catch (error) {
    console.error('连接失败:', error);
}
```

## 事件监听

```typescript
// 监听连接状态
wsService.on('connected', (data) => {
    console.log('已连接:', data.deviceId);
});

wsService.on('disconnected', (data) => {
    console.log('连接断开:', data.code, data.reason);
});

wsService.on('reconnecting', (data) => {
    console.log(`第${data.attempt}次重连中...`);
});

wsService.on('reconnect_failed', (data) => {
    console.log(`重连失败，已尝试${data.attempts}次`);
});

// 监听业务消息
wsService.on('robot-arm-status', (message) => {
    console.log('机械臂状态:', message);
});
```

## 发送消息

```typescript
// 发送消息
try {
    const success = await wsService.send('get-robot-arm', {
        deviceId: 'device-123'
    });
    
    if (success) {
        console.log('消息发送成功');
    } else {
        console.log('消息已入队，等待重连后发送');
    }
} catch (error) {
    console.error('发送失败:', error);
}
```

## 移除事件监听

```typescript
// 移除特定监听器
const handler = (data) => console.log(data);
wsService.on('test-event', handler);
wsService.off('test-event', handler);

// 移除所有监听器
wsService.off('test-event');
```

## 手动关闭连接

```typescript
// 关闭连接（不会自动重连）
wsService.close();
```

## 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| 最大重连次数 | 10 | 超过后停止重连 |
| 基础重连延迟 | 1秒 | 首次重连延迟 |
| 最大重连延迟 | 30秒 | 重连延迟上限 |
| 心跳间隔 | 30秒 | 发送心跳频率 |
| 心跳超时 | 10秒 | 心跳响应超时 |
| 最大心跳失败次数 | 3 | 超过后主动断开 |
| 消息队列大小 | 50 | 队列消息上限 |
| 消息过期时间 | 30秒 | 队列消息有效期 |

## 优化特性

### 1. 指数退避重连
重连延迟：1s → 2s → 4s → 8s → 16s → 30s(最大)

### 2. 智能消息队列
- 连接断开时消息自动入队
- 重连成功后按顺序发送
- 自动清理过期消息
- 队列满时丢弃最旧消息

### 3. 健壮的心跳机制
- 双向心跳：ping/pong
- 超时检测和失败计数
- 连续失败时主动断开重连

### 4. 完善的错误处理
- 连接超时处理
- 消息解析错误处理
- 事件监听器异常处理
- 自动清理出错的监听器

## 与原版对比

| 特性 | 原版 | 优化版 |
|------|------|--------|
| 代码行数 | ~400行 | ~300行 |
| 属性命名 | 下划线前缀 | 简洁命名 |
| 方法结构 | 复杂嵌套 | 职责单一 |
| 错误处理 | 基础处理 | 完善处理 |
| 资源清理 | 手动清理 | 自动清理 |
| 代码可读性 | 一般 | 优秀 |

## 注意事项

1. **设备ID验证**：确保传入有效的设备ID
2. **事件监听器管理**：及时移除不需要的监听器
3. **错误处理**：监听 `error` 事件处理异常情况
4. **资源清理**：应用关闭时调用 `close()` 方法
5. **消息格式**：确保发送的消息数据可序列化为JSON

## 最佳实践

```typescript
class RobotController {
    private wsService: WebsocketService;
    
    constructor() {
        this.wsService = new WebsocketService();
        this.setupEventListeners();
    }
    
    private setupEventListeners() {
        // 连接状态监听
        this.wsService.on('connected', () => {
            this.onConnected();
        });
        
        this.wsService.on('disconnected', () => {
            this.onDisconnected();
        });
        
        // 业务消息监听
        this.wsService.on('robot-status', (data) => {
            this.handleRobotStatus(data);
        });
    }
    
    async connect(deviceId: string) {
        try {
            await this.wsService.init(deviceId);
        } catch (error) {
            console.error('连接失败:', error);
            throw error;
        }
    }
    
    async sendCommand(command: string, params: any) {
        return await this.wsService.send(command, params);
    }
    
    disconnect() {
        this.wsService.close();
    }
    
    private onConnected() {
        console.log('机器人控制器已连接');
    }
    
    private onDisconnected() {
        console.log('机器人控制器已断开');
    }
    
    private handleRobotStatus(data: any) {
        console.log('机器人状态更新:', data);
    }
}
```