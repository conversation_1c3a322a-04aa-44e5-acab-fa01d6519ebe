<template>
  <view class="container">


    <!-- 设备连接状态 -->
    <ConnectionStatus :websocketService="websocketService"/>

    <template v-if="websocketService">
      <DeviceControls
          :websocketService="websocketService"
      />
    </template>


    <!-- 参考物设置 -->
    <view class="reference-section" v-if="photoData">
      <view class="input-group">
        <text class="label">参考物实际长度 (mm):</text>
        <input class="input" type="number" v-model="referenceLength" placeholder="请输入参考物实际长度"/>
        <view class="reference-tip">
          <text class="tip-title">💳 标准银行卡尺寸参考：</text>
          <text class="tip-content">长度：85.60 mm | 宽度：53.98 mm</text>
        </view>
      </view>

      <view class="measure-controls">
        <button class="btn small-btn" @click="startSetReference" v-if="!isSettingReference">开始设置参考点</button>
        <button class="btn small-btn danger" @click="cancelSetReference" v-if="isSettingReference">取消</button>
        <button class="btn small-btn" @click="clearAll">清除所有</button>
      </view>
    </view>

    <!-- 十字准星与照片显示 -->
    <view class="photo-display-section" v-if="isSettingReference || (photoData && physicalDimensions.width)">
      <view class="photo-display">
        <canvas
            canvas-id="photoCanvas"
            id="photoCanvas"
            class="photo-canvas"
            :style="{width: canvasWidth + 'px', height: canvasHeight + 'px'}"
            @tap="onCanvasTap"
        ></canvas>
        <view class="crosshair-overlay" v-if="currentCrosshair && isSettingReference">
          <view class="crosshair-horizontal" :style="{top: currentCrosshair.y + 'px'}"></view>
          <view class="crosshair-vertical" :style="{left: currentCrosshair.x + 'px'}"></view>
        </view>
      </view>

      <!-- 微调控制面板 -->
      <view v-if="isSettingReference">
        <view class="direction-buttons">
          <button class="direction-btn up" @click="moveCrosshair('up')">↑</button>
          <view class="middle-row">
            <button class="direction-btn left" @click="moveCrosshair('left')">←</button>
            <button class="direction-btn confirm" @click="confirmReferencePoint">确定</button>
            <button class="direction-btn right" @click="moveCrosshair('right')">→</button>
          </view>
          <button class="direction-btn down" @click="moveCrosshair('down')">↓</button>
        </view>

        <view class="status-text">
          <text>正在设置参考点 {{ settingPointIndex }}/2</text>
        </view>
      </view>
    </view>

    <!-- 测量结果显示 -->
    <view class="result-section" v-if="physicalDimensions.width">
      <text class="result-title">测量结果:</text>
      <view class="result-item">
        <text>照片物理宽度: {{ physicalDimensions.width.toFixed(2) }} mm</text>
      </view>
      <view class="result-item">
        <text>照片物理高度: {{ physicalDimensions.height.toFixed(2) }} mm</text>
      </view>
      <text class="accuracy-tip">
        * 提示: 结果的准确性取决于参考物长度是否精确，以及拍照时是否正对物体。
      </text>
    </view>

    <!-- 操作提示 -->
    <view class="tips">
      <text>提示：先连接手机设备作为摄像头，然后远程拍照，设置参考物后程序将自动计算照片的物理尺寸。</text>
    </view>
  </view>
</template>

<script lang="ts">
import {WebsocketService} from "../../services/WebsocketService";
import config from "../../config";
import DeviceControls from "../../components/DeviceControls/DeviceControls.vue";
import ConnectionStatus from "../../components/ConnectionStatus/ConnectionStatus.vue";
import {ConfigWebsocketService} from "../../services/websocket/ConfigWebsocketService";
import {TakePhoneSocket} from "../../services/websocket/TakePhoneSocket";

export default {
  components: {
    DeviceControls,
    ConnectionStatus
  },
  data() {
    return {

      websocketService: null,

      // 设备状态
      deviceId: 'pc_control_center',


      currTakePhotoDeviceId: null,
      takePhoneTimer: null,

      // 设备管理
      targetDeviceId: '', // 目标设备ID
      deviceIdOptions: config.deviceIds, // 从配置文件获取设备ID选项
      deviceIdIndex: 0, // 当前选中的设备ID索引，默认选择第一个

      // 多设备管理
      deviceSessions: new Map(), // 存储多个设备的会话数据
      currentSession: null, // 当前活跃的会话

      // 照片和测量相关
      photoData: '',
      referenceLength: 85.60,
      physicalDimensions: {
        width: null,
        height: null,
      },
      canvasWidth: 300,
      canvasHeight: 400,
      referencePoints: [],
      scaleFactor: 0,
      isSettingReference: false,
      currentCrosshair: null,
      settingPointIndex: 0,
    }
  },
  mounted() {
    // 初始化设备ID选择
    if (this.deviceIdOptions.length > 0) {
      this.targetDeviceId = this.deviceIdOptions[this.deviceIdIndex];
      console.log('初始设备ID:', this.targetDeviceId);
    }

    // 初始化WebSocket连接
    this.initWebSocket();
  },
  beforeDestroy() {
    this.cleanup();
  },
  methods: {
    initWebSocket() {
      // 初始化WebSocket服务，使用固定的PC设备ID
      console.log('初始化WebSocket服务，PC设备ID:', this.deviceId);

      this.websocketService = new WebsocketService()


      try {
        this.websocketService.init('pc', this.deviceId);
        console.log('WebSocket服务初始化成功，设备ID:', this.deviceId);
      } catch (error) {
        console.error('WebSocket服务初始化失败:', error);
        uni.showToast({title: 'WebSocket服务初始化失败', icon: 'error'});
      }


      // 监听发送失败事件
      this.websocketService.on('send_failed', (data) => {
        console.error('消息发送失败:', data);
        uni.showToast({title: '消息发送失败，请检查网络连接', icon: 'error'});
      });

      TakePhoneSocket.receivePhoneByMobile(this.websocketService, (photoData, deviceId) => {
        console.log('photoData')
        console.log('deviceId:',deviceId)
        console.log(photoData)
        this.currTakePhotoDeviceId = deviceId;
        this.referencePoints = [];
        this.physicalDimensions = {width: null, height: null};
        this.scaleFactor = 0;
        this.isSettingReference = false;
        this.settingPointIndex = 0;

        // 设置新照片
        this.photoData = URL.createObjectURL(photoData);

        // 获取照片信息
        uni.getImageInfo({
          src: this.photoData,
          success: (res) => {
            this.canvasWidth = res.width;
            this.canvasHeight = res.height;
            this.currentCrosshair = {
              x: Math.floor(res.width / 2),
              y: Math.floor(res.height / 2)
            };
          }
        });

        uni.showToast({title: '照片接收成功', icon: 'success'});

      })


    },

    cleanup() {
      // 关闭WebSocket服务
      this.websocketService.close();
    },




    // 以下方法与原size.vue相同
    drawPhotoToCanvas() {
      if (!this.photoData) return;
      const photoCtx = uni.createCanvasContext('photoCanvas', this);
      photoCtx.drawImage(this.photoData, 0, 0, this.canvasWidth, this.canvasHeight);
      photoCtx.draw();
    },

    startSetReference() {
      this.isSettingReference = true;
      this.settingPointIndex = 1;
      this.physicalDimensions = {width: null, height: null};
      this.referencePoints = [];
      this.currentCrosshair = {
        x: Math.floor(this.canvasWidth / 2),
        y: Math.floor(this.canvasHeight / 2)
      };
      this.$nextTick(() => {
        this.drawPhotoToCanvas();
      });
    },

    cancelSetReference() {
      this.isSettingReference = false;
      this.settingPointIndex = 0;
      this.referencePoints = [];
      if (this.physicalDimensions.width) {
        this.drawReferenceLine();
      } else {
        this.drawPhotoToCanvas();
      }
    },

    onCanvasTap(e) {
      if (!this.isSettingReference) return;

      const query = uni.createSelectorQuery().in(this);
      query.select('#photoCanvas').boundingClientRect();
      query.exec(res => {
        const canvasRect = res[0];
        if (canvasRect) {
          const x = Math.round(e.detail.x - canvasRect.left);
          const y = Math.round(e.detail.y - canvasRect.top);
          this.currentCrosshair.x = Math.max(0, Math.min(this.canvasWidth, x));
          this.currentCrosshair.y = Math.max(0, Math.min(this.canvasHeight, y));
        }
      });
    },

    moveCrosshair(direction) {
      if (!this.currentCrosshair) return;
      const step = 5;
      switch (direction) {
        case 'up':
          this.currentCrosshair.y = Math.max(0, this.currentCrosshair.y - step);
          break;
        case 'down':
          this.currentCrosshair.y = Math.min(this.canvasHeight, this.currentCrosshair.y + step);
          break;
        case 'left':
          this.currentCrosshair.x = Math.max(0, this.currentCrosshair.x - step);
          break;
        case 'right':
          this.currentCrosshair.x = Math.min(this.canvasWidth, this.currentCrosshair.x + step);
          break;
      }
    },

    confirmReferencePoint() {
      if (this.settingPointIndex <= 2) {
        this.referencePoints.push({...this.currentCrosshair});

        if (this.settingPointIndex === 1) {
          this.settingPointIndex = 2;
          uni.showToast({title: '已设置第1个点，请设置第2个', icon: 'none'});
        } else {
          this.isSettingReference = false;

          const refDistance = this.calculateDistance(this.referencePoints[0], this.referencePoints[1]);
          if (refDistance === 0 || !this.referenceLength) {
            uni.showToast({title: '参考距离或长度不能为0', icon: 'error'});
            return;
          }

          this.scaleFactor = this.referenceLength / refDistance;

          this.physicalDimensions.width = this.canvasWidth * this.scaleFactor;
          this.physicalDimensions.height = this.canvasHeight * this.scaleFactor;

          this.drawReferenceLine();

          // 推送物理尺寸数据到手机端
          this.pushPhysicalDimensionsToMobile();

          uni.showToast({title: '尺寸计算完成并已推送到手机端', icon: 'success'});
        }
      }
    },

    calculateDistance(point1, point2) {
      const dx = point2.x - point1.x;
      const dy = point2.y - point1.y;
      return Math.sqrt(dx * dx + dy * dy);
    },

    drawReferenceLine() {
      if (this.referencePoints.length < 2) return;

      const photoCtx = uni.createCanvasContext('photoCanvas', this);

      photoCtx.drawImage(this.photoData, 0, 0, this.canvasWidth, this.canvasHeight);

      photoCtx.setStrokeStyle('#007AFF');
      photoCtx.setLineWidth(3);
      photoCtx.beginPath();
      photoCtx.moveTo(this.referencePoints[0].x, this.referencePoints[0].y);
      photoCtx.lineTo(this.referencePoints[1].x, this.referencePoints[1].y);
      photoCtx.stroke();

      const midX = (this.referencePoints[0].x + this.referencePoints[1].x) / 2;
      const midY = (this.referencePoints[0].y + this.referencePoints[1].y) / 2;

      photoCtx.setFillStyle('#007AFF');
      photoCtx.setFontSize(14);
      photoCtx.setTextAlign('center');
      photoCtx.fillText(`参考: ${this.referenceLength}mm`, midX, midY - 15);

      photoCtx.draw();
    },

    clearAll() {
      this.photoData = '';
      this.referencePoints = [];
      this.physicalDimensions = {width: null, height: null};
      this.scaleFactor = 0;
      this.isSettingReference = false;
      this.settingPointIndex = 0;

      const photoCtx = uni.createCanvasContext('photoCanvas', this);
      photoCtx.clearRect(0, 0, this.canvasWidth, this.canvasHeight);
      photoCtx.draw();
    },

    pushPhysicalDimensionsToMobile() {
      if (!this.physicalDimensions.width) {
        console.log('无法推送：未配对手机或无物理尺寸数据');
        return;
      }

      try {
        ConfigWebsocketService.setConfig(this.currTakePhotoDeviceId, {
          physicalHeight: this.physicalDimensions.width.toFixed(2),
          physicalWidth: this.physicalDimensions.height.toFixed(2),
        }, this.websocketService)
        console.log('物理尺寸数据推送成功');
      } catch (error) {
        console.error('推送物理尺寸数据失败:', error);
        uni.showToast({title: '推送数据失败', icon: 'error'});
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}


.photo-controls {
  background-color: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin: 30rpx 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  text-align: center;
}


.btn {
  background-color: #007AFF;
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 10rpx;
  font-size: 32rpx;
  margin: 10rpx;

  &:disabled {
    background-color: #ccc;
  }
}

.small-btn {
  padding: 15rpx 30rpx;
  font-size: 28rpx;
  margin: 5rpx;
}

.danger {
  background-color: #FF5252;
}

.reference-section {
  background-color: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin: 30rpx 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.input-group {
  margin-bottom: 20rpx;
}

.label {
  display: block;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #666;
}

.input {
  width: 100%;
  padding: 15rpx;
  border: 1rpx solid #ddd;
  border-radius: 5rpx;
  font-size: 28rpx;
}

.result-section {
  background-color: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin: 30rpx 0;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}

.result-item {
  padding: 15rpx;
  background-color: #f9f9f9;
  margin-bottom: 10rpx;
  border-radius: 5rpx;
}

.accuracy-tip {
  display: block;
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.photo-display-section {
  margin: 30rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.photo-display {
  position: relative;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  overflow: hidden;
}

.photo-canvas {
  display: block;
}

.crosshair-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.crosshair-horizontal {
  position: absolute;
  left: 0;
  right: 0;
  height: 1rpx;
  background-color: #FF5722;
  opacity: 0.8;
}

.crosshair-vertical {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1rpx;
  background-color: #FF5722;
  opacity: 0.8;
}

.direction-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20rpx 0;
}

.direction-btn {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #007AFF;
  color: white;
  font-size: 32rpx;
  margin: 5rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.middle-row {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.confirm {
  background-color: #4CAF50;
  word-break: keep-all;
}

.measure-controls {
  margin: 20rpx 0;
  text-align: center;
}

.status-text {
  margin: 15rpx 0;
  text-align: center;
  font-size: 28rpx;
  color: #666;
}


.reference-tip {
  background-color: #e8f5e8;
  padding: 15rpx;
  border-radius: 8rpx;
  margin-top: 15rpx;
  border-left: 4rpx solid #4CAF50;
}

.tip-title {
  display: block;
  font-size: 26rpx;
  color: #4CAF50;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.tip-content {
  display: block;
  font-size: 24rpx;
  color: #2e7d32;
}

.tips {
  background-color: #FFF3CD;
  padding: 20rpx;
  border-radius: 10rpx;
  margin: 30rpx 0;
  font-size: 24rpx;
  color: #856404;
  line-height: 1.5;
}
</style>
