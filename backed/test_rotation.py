#!/usr/bin/env python3
"""
测试OCR API的图像旋转功能
"""

import requests
from PIL import Image, ImageDraw, ImageFont

def create_test_image(width=720, height=1280, text="测试文字 Test Text"):
    """创建一个测试图像"""
    # 创建白色背景图像
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 40)
    except:
        # 如果没有找到字体，使用默认字体
        font = ImageFont.load_default()
    
    # 在图像中央绘制文字
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    draw.text((x, y), text, fill='black', font=font)
    
    return img



def test_ocr_rotation(base_url="http://localhost:8000"):
    """测试OCR旋转功能"""
    
    print("🧪 创建测试图像 (720x1280)...")
    test_img = create_test_image(720, 1280, "这是一个竖屏测试图片\nTest Portrait Image")
    
    # 保存测试图像以供查看
    test_filename = "test_portrait_720x1280.jpg"
    test_img.save(test_filename)
    print(f"📁 测试图像已保存为: {test_filename}")
    
    print(f"\n🔍 测试OCR API旋转功能...")
    print(f"📐 原始图像尺寸: {test_img.size}")
    
    # 测试不同的旋转设置
    test_cases = [
        {
            "name": "默认旋转90度",
            "params": {
                "rotation_angle": 90
            }
        },
        {
            "name": "旋转180度",
            "params": {
                "rotation_angle": 180
            }
        },
        {
            "name": "不旋转",
            "params": {
                "rotation_angle": 0
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_case['name']} ---")
        
        try:
            # 准备文件上传
            with open(test_filename, 'rb') as f:
                files = {'file': (test_filename, f, 'image/jpeg')}
                data = test_case["params"]
                
                # 发送请求
                response = requests.post(f"{base_url}/ocr/recognize", files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get("success"):
                    print(f"✅ 识别成功!")
                    print(f"📊 识别到 {result.get('text_count', 0)} 段文字")
                    print(f"🔄 旋转角度: {result.get('rotation_applied', 0)}°")
                    
                    # 显示图像尺寸变化
                    if result.get("image_size"):
                        orig = result["image_size"]["original"]
                        proc = result["image_size"]["processed"]
                        print(f"📏 原始尺寸: {orig['width']}x{orig['height']}")
                        print(f"📏 处理后尺寸: {proc['width']}x{proc['height']}")
                    
                    # 显示识别的文字
                    if result.get("full_text"):
                        print(f"📝 识别文字: {result['full_text']}")
                    
                    # 显示详细信息
                    if result.get("details"):
                        print(f"📋 详细信息:")
                        for j, detail in enumerate(result["details"], 1):
                            conf = detail.get("confidence", 0) * 100
                            print(f"   {j}. \"{detail.get('text', '')}\" (置信度: {conf:.1f}%)")
                else:
                    print(f"❌ 识别失败: {result}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"   响应: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")

def test_health_check(base_url="http://localhost:8000"):
    """测试服务健康状态"""
    print("🏥 检查OCR服务状态...")
    
    try:
        response = requests.get(f"{base_url}/ocr/health")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 服务状态: {result.get('status', 'unknown')}")
            print(f"🔧 OCR引擎: {result.get('ocr_engine', 'unknown')}")
            print(f"⚙️  运行模式: {result.get('mode', 'unknown')}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")

if __name__ == "__main__":
    print("🚀 OCR旋转功能测试")
    print("=" * 50)
    
    # 首先检查服务状态
    test_health_check()
    
    print("\n" + "=" * 50)
    
    # 测试旋转功能
    test_ocr_rotation()
    
    print("\n" + "=" * 50)
    print("✨ 测试完成!")