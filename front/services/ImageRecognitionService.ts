import Config from "../config";
import {mobileLog} from "../utils/funs";


/**
 * YOLO Detection Result Interface
 */
export interface YoloDetectionResult {
    class_id: number;
    class_name: string;
    confidence: number;
    bbox: [number, number, number, number];
    // 调用本次执行识别时候的机械臂位置
    currX: number;
    currY: number;
    // 最终点击位置
    finalX: number;
    finalY: number;
}

/**
 * Successful Response Interface
 */
export interface YoloDetectionSuccessResponse {
    results: YoloDetectionResult[];
}


/**
 * 图片识别服务
 * 负责处理图片上传、YOLO识别等功能
 */
export class ImageRecognitionService {


    /**
     * 拍照并进行YOLO识别
     */
    async captureAndRecognize(): Promise<YoloDetectionSuccessResponse | null> {
        return new Promise(async (resolve) => {
            uni.$emit('takePhoto', {
                compressionQuality: 0.85,
                callback: (phoneData: any) => {
                    mobileLog('拍照完成，开始异步上传和识别', 'info');

                    // 直接上传处理后的图片数据（已在 takePhoto 中完成压缩）
                    this.uploadAndRecognize(phoneData).then((result: YoloDetectionSuccessResponse | null) => {
                        resolve(result);
                    }).catch(error => {
                        mobileLog(`识别过程出错: ${error.message}`, 'error');
                        resolve(null);
                    });
                }
            })
        })

    }


    /**
     * 上传图片并进行YOLO识别
     * @param {Blob} blob 图片Blob对象
     * @returns {Promise<YoloDetectionSuccessResponse|null>} 识别结果
     */
    async uploadAndRecognize(blob: any): Promise<YoloDetectionSuccessResponse | null> {
        // 创建FormData并上传到后端
        const formData = new FormData();
        formData.append('file', blob, 'photo.jpg'); // 压缩后为JPEG格式

        const protocol = Config.useSSL ? 'https' : 'http';
        const apiUrl = `${protocol}://${Config.host}:${Config.port}/yolo/detect`;

        const response = await fetch(apiUrl, {
            method: 'POST', body: formData
        });

        if (response.ok) {
            const result = await response.json();

            // 显示识别结果
            const detectedCount = result.results ? result.results.length : 0;
            mobileLog(`YOLO识别完成: 检测到${detectedCount}个目标;` + JSON.stringify(result), detectedCount > 0 ? 'success' : 'info');


            return result;
        } else {
            mobileLog(`YOLO识别失败: ${response.statusText}`, 'error');
            return null;
        }
    }
}