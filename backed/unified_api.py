"""
统一识别API
提供统一的文件上传和识别接口，整合OCR和YOLO功能
"""

from fastapi import APIRouter, File, UploadFile, BackgroundTasks, HTTPException
from fastapi.responses import JSONResponse
import logging

from services.unified_recognition_service import unified_recognition_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/unified", tags=["Unified Recognition"])

@router.post("/recognize")
async def unified_recognize(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...)
):
    """
    统一识别API - 同时执行OCR和YOLO识别
    
    功能特点:
    - 支持720x1280图像上传
    - 异步并行执行OCR和YOLO识别
    - OCR使用旋转90度后的图像进行识别
    - YOLO使用原图进行识别
    - 统一返回OCR坐标系的结果
    - 自动保存检测目标<=1的图像
    
    参数:
    - file: 上传的图像文件 (支持JPG, PNG, BMP, TIFF, WebP)
    
    返回:
    - success: 识别是否成功
    - total_detections: 总检测数量
    - ocr_count: OCR识别到的文字数量
    - yolo_count: YOLO识别到的目标数量
    - detections: 统一格式的检测结果列表
      - name: 文字内容或目标名称
      - type: "ocr" 或 "yolo"
      - confidence: 置信度 (0.0-1.0)
      - center_x, center_y: 中心坐标点
      - bounding_box: 详细边界框信息
    - image_info: 图像信息
    - processing_info: 处理信息
    """
    
    try:
        # 调用统一识别服务
        result = await unified_recognition_service.process_image_upload(file, background_tasks)
        
        return JSONResponse(content=result)
        
    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        logger.error(f"统一识别API失败: {e}")
        raise HTTPException(status_code=500, detail=f"识别失败: {str(e)}")

@router.get("/health")
async def health_check():
    """
    健康检查 - 检查所有服务状态
    
    返回:
    - unified_service: 统一服务状态
    - ocr_service: OCR服务状态
    - yolo_service: YOLO服务状态
    - features: 支持的功能列表
    """
    try:
        status = unified_recognition_service.get_service_status()
        return JSONResponse(content=status)
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": f"健康检查失败: {str(e)}"}
        )

@router.get("/info")
async def get_service_info():
    """
    获取服务信息
    
    返回详细的服务配置和功能信息
    """
    return {
        "service": "统一识别API",
        "version": "1.0.0",
        "description": "整合OCR和YOLO的统一识别服务",
        "supported_formats": ["JPG", "PNG", "BMP", "TIFF", "WebP"],
        "max_file_size": "10MB",
        "image_requirements": {
            "recommended_size": "720x1280",
            "orientation": "竖屏",
            "processing": {
                "ocr": "旋转90度后识别",
                "yolo": "原图识别"
            }
        },
        "coordinate_system": {
            "output": "OCR坐标系 (旋转90度后)",
            "conversion": "YOLO坐标自动转换为OCR坐标系",
            "description": "所有返回结果使用统一的OCR坐标系"
        },
        "features": [
            "统一文件上传验证",
            "异步并行识别 (OCR + YOLO)",
            "坐标系统一转换",
            "自动图像保存 (检测目标<=1时)",
            "详细边界框信息",
            "置信度评估",
            "健康状态监控"
        ],
        "output_format": {
            "detections": [
                {
                    "name": "文字内容或目标名称",
                    "type": "ocr | yolo",
                    "confidence": "置信度 (0.0-1.0)",
                    "center_x": "中心点X坐标",
                    "center_y": "中心点Y坐标",
                    "bounding_box": {
                        "rectangle": "矩形边界框",
                        "points": "四角点坐标",
                        "opencv_rect": "OpenCV格式"
                    }
                }
            ]
        },
        "endpoints": {
            "/unified/recognize": "统一识别接口",
            "/unified/health": "健康检查",
            "/unified/info": "服务信息"
        }
    }

# 兼容性接口 - 保持与原有API的兼容性
@router.post("/ocr")
async def ocr_recognize_compat(file: UploadFile = File(...)):
    """OCR识别兼容接口"""
    try:
        from services.ocr_service import ocr_service
        from services.image_utils import FileUploadHandler
        
        # 处理文件上传
        image, _ = await FileUploadHandler.process_upload_file(file)
        
        # 执行OCR识别
        result = ocr_service.recognize_text(image, rotation_angle=90)
        
        return JSONResponse(content=result)
        
    except Exception as e:
        logger.error(f"OCR兼容接口失败: {e}")
        raise HTTPException(status_code=500, detail=f"OCR识别失败: {str(e)}")

@router.post("/yolo")
async def yolo_detect_compat(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...)
):
    """YOLO检测兼容接口"""
    try:
        from services.yolo_service import yolo_service
        from services.image_utils import FileUploadHandler
        
        if yolo_service is None:
            raise HTTPException(status_code=503, detail="YOLO服务不可用")
        
        # 处理文件上传
        image, image_bytes = await FileUploadHandler.process_upload_file(file)
        
        # 获取图像尺寸
        original_height, original_width = image.shape[:2]
        original_size = (original_width, original_height)
        
        # 执行YOLO检测 (不转换坐标，保持原始格式)
        results = yolo_service.detect_objects(
            image_bytes, 
            original_size=original_size, 
            convert_to_ocr_coords=False
        )
        
        # 后台保存图像
        if len(results) <= 1:
            background_tasks.add_task(
                yolo_service.save_image_if_needed, 
                image_bytes, 
                file.filename, 
                len(results)
            )
        
        # 转换为原始YOLO API格式
        yolo_format_results = []
        for item in results:
            bbox = item["bounding_box"]["rectangle"]
            yolo_format_results.append({
                "class_id": item.get("class_id", 0),
                "class_name": item["name"],
                "confidence": item["confidence"],
                "bbox": [bbox["x_min"], bbox["y_min"], bbox["x_max"], bbox["y_max"]]
            })
        
        return JSONResponse(content={"results": yolo_format_results})
        
    except Exception as e:
        logger.error(f"YOLO兼容接口失败: {e}")
        raise HTTPException(status_code=500, detail=f"YOLO检测失败: {str(e)}")
