/**
 * 记录所有设备当前的任务状态
 */
import {ConfigService} from "../ConfigService";
import {TaskRunner} from "../TaskRunner";
import {mobileLog} from "../../utils/funs";
import {WebsocketService} from "../WebsocketService";


let _taskStatus = false;

export class TaskStatusSocket {


    static getStatus() {
        return _taskStatus;
    }


    static setupEventListeners(websocketService: WebsocketService) {
        TaskStatusSocket.onGetTaskStatus(websocketService)
        TaskStatusSocket.onStopTask(websocketService)
        TaskStatusSocket.onStartTask(websocketService, () => {
            mobileLog('收到开始任务事件', 'info');


            // 创建新的 TaskRunner 实例
            TaskRunner.startTask().then(_ => {
            });
        })
    }


    /**
     * 获取设备当前的任务状态
     * @param websocketService
     * @param deviceId
     * @param callback
     */
    static getTaskStatus(websocketService: WebsocketService, deviceId: string, callback: Function) {
        websocketService.on('get-task-status-return', (res) => {
            const status = res.json.status;
            callback(deviceId, status);
        });


        websocketService.send('get-task-status', {
            deviceId: deviceId,
        }).then(() => {
        });
    }


    static onGetTaskStatus(websocketService: WebsocketService) {
        websocketService.on('get-task-status', (res: any) => {
            const deviceId = res.json.deviceId;
            if (deviceId === ConfigService.getDeviceId()) {
                websocketService.send('get-task-status-return', {
                    status: _taskStatus,
                }).then(_ => {
                });
            }
        })
    }

    /**
     * 通知任务开始
     * @param websocketService
     * @param deviceId
     * @param callback
     */
    static notifyStartTask(websocketService: WebsocketService, deviceId: string, callback: Function) {

        websocketService.on('notify-task-start-return', (res) => {
            if (res.json.deviceId === deviceId) {
                callback && callback();
            }
        })

        websocketService.send('notify-task-start', {
            deviceId: deviceId,
        }).then(() => {
        });
    }


    /**
     * 监听任务开始
     * @param websocketService
     * @param callback
     */
    static onStartTask(websocketService: WebsocketService, callback: Function) {
        websocketService.on('notify-task-start', (res) => {
            const deviceId = res.json.deviceId;
            if (deviceId === ConfigService.getDeviceId()) {
                _taskStatus = true;

                websocketService.send('notify-task-start-return', {
                    deviceId: deviceId,
                }).then(() => {
                    callback();
                });

            }
        });
    }


    /**
     * 通知任务停止
     * @param websocketService
     * @param deviceId
     * @param callback
     */
    static notifyStopTask(websocketService: WebsocketService, deviceId: string, callback: Function) {

        websocketService.on('notify-task-stop-return', (res) => {
            if (res.json.deviceId === deviceId) {
                callback && callback();
            }
        })

        websocketService.send('notify-task-stop', {
            deviceId: deviceId,
        }).then(() => {
        });
    }

    /**
     * 监听任务停止
     * @param websocketService
     */
    static onStopTask(websocketService: WebsocketService) {
        websocketService.on('notify-task-stop', (res: any) => {
            const deviceId = res.json.deviceId;
            if (deviceId === ConfigService.getDeviceId()) {
                _taskStatus = false;

                websocketService.send('notify-task-stop-return', {
                    deviceId: deviceId,
                }).then(() => {
                });

            }
        });
    }


}