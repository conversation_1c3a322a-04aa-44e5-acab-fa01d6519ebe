
interface CacheData {
    data: any
    expired: number
}

const appVersion = uni.getAppBaseInfo().appVersionCode
export class CacheMgr {

    public static set(key: string, val: any, expired ?: number) {
        let dte = new Date();
        let data: CacheData = {
            data: val,
            expired: dte.getTime() / 1000 + (expired ? expired : 86400 * 365)
        }
        uni.setStorage({
            key: `var:${appVersion}:${key}`,
            data: JSON.stringify(data)
        });
    }


    public static get(key: string, isDel: boolean = false) {

        let cacheKey = `var:${appVersion}:${key}`

        let cacheStr = uni.getStorageSync(cacheKey);
        if (cacheStr) {
            let data: CacheData = JSON.parse(cacheStr);
            let dte = new Date();
            if (data.expired > dte.getTime() / 1000) {
                if (isDel) {
                    CacheMgr.del(key);
                }
                return data.data;
            } else {
                CacheMgr.del(key);
            }
        }
        return null;
    }


    public static del(key: string) {
        let cacheKey = `var:${appVersion}:${key}`
        uni.removeStorageSync(cacheKey);
    }


    public static clear() {
        uni.clearStorage();
    }

}