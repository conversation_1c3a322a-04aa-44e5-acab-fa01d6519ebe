/**
 * 打开端口相关服务
 */
import { ConfigService } from "../ConfigService";
import { RobotArmService } from "../RobotArmService";
import { mobileLog, showModal } from "../../utils/funs";
import { WebsocketService } from "../WebsocketService";


let _robotArmService: RobotArmService | null;

export class RobotArmSocket {


    static getRobotArmService(): RobotArmService | undefined {
        if (!_robotArmService) {
            showModal({
                title: '请先打开端口',
                showCancel: false,
            })
            return undefined;
        }
        return _robotArmService;
    }

    static setupEventListeners(websocketService: WebsocketService) {
        RobotArmSocket.onOpenHandle(websocketService)
        RobotArmSocket.onCloseHandle(websocketService)
        RobotArmSocket.onGetStatus(websocketService)
        RobotArmSocket.onReset(websocketService)
        RobotArmSocket.onMoveTo(websocketService)
        RobotArmSocket.onMoveToByPx(websocketService)
        RobotArmSocket.onPress(websocketService)
        RobotArmSocket.onRelease(websocketService)
        RobotArmSocket.onClick(websocketService)
        RobotArmSocket.onSwipe(websocketService)
    }

    /**
     * 获取机械臂打开状态
     * @param websocketService
     * @param deviceId
     */
    static getStatus(websocketService: WebsocketService, deviceId: string): Promise<{ handle: number, port: string, deviceId: string }> {
        return new Promise((resolve) => {
            websocketService.on('get-robot-arm-return', (res: any) => {
                resolve({
                    handle: res.json.handle,
                    port: res.json.port,
                    deviceId,
                });
            });

            websocketService.send('get-robot-arm', {
                deviceId: deviceId,
            }).then(() => {
            });
        });
    }

    static onGetStatus(websocketService: WebsocketService) {
        websocketService.on('get-robot-arm', (res: any) => {
            const deviceId = res.json.deviceId;
            mobileLog('收到查询 机械臂是否打开事件')
            if (deviceId === ConfigService.getDeviceId()) {
                websocketService.send('get-robot-arm-return', {
                    handle: _robotArmService ? _robotArmService.handle : 0,
                    port: _robotArmService ? _robotArmService.port : '',
                }).then(() => {
                });
            }
        })
    }


    /**
     * 通知机械臂打开
     * @param websocketService
     * @param deviceId
     * @param port
     */
    static notifyOpenHandle(websocketService: WebsocketService, deviceId: string, port: string): Promise<any> {
        return new Promise((resolve) => {
            websocketService.on('notify-robot-arm-open-return', (res: any) => {
                console.log('收到事件：notify-robot-arm-open-return');
                console.log(res);
                if (deviceId === res.json.deviceId) {
                    resolve(res);
                }
            });

            websocketService.send('notify-robot-arm-open', {
                deviceId: deviceId, port: port,
            }).then(() => {
            });
        });
    }


    /**
     * 监听机械臂打开
     * @param websocketService
     */
    static onOpenHandle(websocketService: WebsocketService) {
        websocketService.on('notify-robot-arm-open', async (res: any) => {
            const deviceId = res.json.deviceId;
            const port = res.json.port;
            if (deviceId === ConfigService.getDeviceId()) {
                const robotArmService = await RobotArmService.openHandle(port);
                if (robotArmService) {
                    _robotArmService = robotArmService;
                    await websocketService.send('notify-robot-arm-open-return', {
                        deviceId: deviceId,
                        port: port,
                        handle: _robotArmService.handle,
                    });
                } else {
                    // 处理打开失败的情况
                    await websocketService.send('notify-robot-arm-open-return', {
                        deviceId: deviceId,
                        port: port,
                        handle: 0,
                        error: '端口打开失败'
                    });
                }
            }
        });
    }


    /**
     * 通知机械臂关闭
     * @param websocketService
     * @param deviceId
     */
    static notifyCloseHandle(websocketService: WebsocketService, deviceId: string): Promise<any> {
        return new Promise((resolve) => {
            websocketService.on('notify-robot-arm-stop-return', (res: any) => {
                if (deviceId === res.json.deviceId) {
                    resolve(res);
                }
            });

            websocketService.send('notify-robot-arm-stop', {
                deviceId: deviceId,
            }).then(() => {
            });
        });
    }

    /**
     * 监听械臂关闭
     * @param websocketService
     */
    static onCloseHandle(websocketService: WebsocketService) {
        websocketService.on('notify-robot-arm-stop', async (res: any) => {
            const deviceId = res.json.deviceId;
            if (deviceId === ConfigService.getDeviceId()) {
                if (_robotArmService) {
                    await _robotArmService.closeHandle()
                    _robotArmService = null;
                }

                await websocketService.send('notify-robot-arm-stop-return', {
                    deviceId: deviceId,
                });

            }
        });
    }

    // ==================== 移动端监听方法 ====================

    /**
     * 监听机械臂重置
     * @param websocketService
     */
    static onReset(websocketService: WebsocketService) {
        websocketService.on('notify-robot-arm-reset', async (res: any) => {
            const deviceId = res.json.deviceId;
            if (deviceId === ConfigService.getDeviceId()) {
                mobileLog('收到机械臂重置事件');
                let result;
                if (_robotArmService) {
                    result = await _robotArmService.reset();
                }

                await websocketService.send('notify-robot-arm-reset-return', {
                    deviceId: deviceId,
                    status: result,
                });
            }
        });
    }

    /**
     * 监听机械臂移动到坐标
     * @param websocketService
     */
    static onMoveTo(websocketService: WebsocketService) {
        websocketService.on('notify-robot-arm-moveto', async (res: any) => {
            const deviceId = res.json.deviceId;
            const x = res.json.x;
            const y = res.json.y;
            if (deviceId === ConfigService.getDeviceId()) {
                mobileLog(`收到机械臂移动事件: x=${x}, y=${y}`);
                let result;
                if (_robotArmService) {
                    result = await _robotArmService.moveTo(x, y);
                }

                await websocketService.send('notify-robot-arm-moveto-return', {
                    deviceId: deviceId,
                    status: result,
                })

            }
        });
    }

    /**
     * 监听机械臂通过像素移动
     * @param websocketService
     */
    static onMoveToByPx(websocketService: WebsocketService) {
        websocketService.on('notify-robot-arm-movetobypx', async (res: any) => {
            const deviceId = res.json.deviceId;
            const x = res.json.x;
            const y = res.json.y;
            if (deviceId === ConfigService.getDeviceId()) {
                mobileLog(`收到机械臂像素移动事件: x=${x}, y=${y}`);
                let result;
                if (_robotArmService) {
                    result = await _robotArmService.moveToByPx(x, y);
                }

                await websocketService.send('notify-robot-arm-movetobypx-return', {
                    deviceId: deviceId,
                    status: result,
                });
            }
        });
    }

    /**
     * 监听机械臂按下
     * @param websocketService
     */
    static onPress(websocketService: WebsocketService) {
        websocketService.on('notify-robot-arm-press', async (res: any) => {
            const deviceId = res.json.deviceId;
            const z = res.json.z || 8;
            if (deviceId === ConfigService.getDeviceId()) {
                mobileLog(`收到机械臂按下事件: z=${z}`);
                let result;
                if (_robotArmService) {
                    result = await _robotArmService.press(z);
                }

                await websocketService.send('notify-robot-arm-press-return', {
                    deviceId: deviceId,
                    status: result,
                });
            }
        });
    }

    /**
     * 监听机械臂抬起
     * @param websocketService
     */
    static onRelease(websocketService: WebsocketService) {
        websocketService.on('notify-robot-arm-release', async (res: any) => {
            const deviceId = res.json.deviceId;
            if (deviceId === ConfigService.getDeviceId()) {
                mobileLog('收到机械臂抬起事件');

                let result;
                if (_robotArmService) {
                    result = await _robotArmService.release();
                }

                await websocketService.send('notify-robot-arm-release-return', {
                    deviceId: deviceId,
                    status: result,
                });
            }
        });
    }

    /**
     * 监听机械臂点击
     * @param websocketService
     */
    static onClick(websocketService: WebsocketService) {
        websocketService.on('notify-robot-arm-click', async (res: any) => {
            const deviceId = res.json.deviceId;
            const x = res.json.x;
            const y = res.json.y;
            if (deviceId === ConfigService.getDeviceId()) {
                mobileLog(`收到机械臂点击事件: x=${x}, y=${y}`);
                let result;
                if (_robotArmService) {
                    result = await _robotArmService.click(x, y);
                }

                await websocketService.send('notify-robot-arm-click-return', {
                    deviceId: deviceId,
                    status: result,
                });
            }
        });
    }

    /**
     * 监听机械臂滑动
     * @param websocketService
     */
    static onSwipe(websocketService: WebsocketService) {
        websocketService.on('notify-robot-arm-swipe', async (res: any) => {
            const deviceId = res.json.deviceId;
            const x1 = res.json.x1;
            const y1 = res.json.y1;
            const x2 = res.json.x2;
            const y2 = res.json.y2;
            if (deviceId === ConfigService.getDeviceId()) {
                mobileLog(`收到机械臂滑动事件: from(${x1},${y1}) to(${x2},${y2})`);
                let result;
                if (_robotArmService) {
                    result = await _robotArmService.swipe(x1, y1, x2, y2);
                }

                await websocketService.send('notify-robot-arm-swipe-return', {
                    deviceId: deviceId,
                    status: result,
                });
            }
        });
    }

    // ==================== PC端发送事件执行方法 ====================

    /**
     * 通知机械臂重置
     * @param websocketService
     * @param deviceId
     */
    static notifyReset(websocketService: WebsocketService, deviceId: string) {
        return new Promise(async (resolve) => {
            websocketService.on('notify-robot-arm-reset-return', res => {
                if (deviceId === res.json.deviceId) {
                    resolve(res.json.status);
                }
            });

            await websocketService.send('notify-robot-arm-reset', {
                deviceId: deviceId,
            })
        })

    }

    /**
     * 通知机械臂移动到指定坐标
     * @param websocketService
     * @param deviceId
     * @param x
     * @param y
     */
    static notifyMoveTo(websocketService: WebsocketService, deviceId: string, x: number, y: number): Promise<any> {
        return new Promise((resolve) => {
            websocketService.on('notify-robot-arm-moveto-return', res => {
                if (deviceId === res.json.deviceId) {
                    resolve(res.json.status);
                }
            });

            websocketService.send('notify-robot-arm-moveto', {
                deviceId: deviceId,
                x: x,
                y: y,
            }).then(() => {
            });
        });
    }

    /**
     * 通知机械臂通过像素坐标移动
     * @param websocketService
     * @param deviceId
     * @param x
     * @param y
     */
    static notifyMoveToByPx(websocketService: WebsocketService, deviceId: string, x: number, y: number): Promise<any> {
        return new Promise((resolve) => {
            websocketService.on('notify-robot-arm-movetobypx-return', (res: any) => {
                if (deviceId === res.json.deviceId) {
                    resolve(res.json.status);
                }
            });

            websocketService.send('notify-robot-arm-movetobypx', {
                deviceId: deviceId,
                x: x,
                y: y,
            }).then(() => {
            });
        });
    }

    /**
     * 通知机械臂按下（触控笔下降）
     * @param websocketService
     * @param deviceId
     * @param z
     */
    static notifyPress(websocketService: WebsocketService, deviceId: string, z: number = 8): Promise<any> {
        return new Promise((resolve) => {
            websocketService.on('notify-robot-arm-press-return', (res: any) => {
                if (deviceId === res.json.deviceId) {
                    resolve(res.json.status);
                }
            });

            websocketService.send('notify-robot-arm-press', {
                deviceId: deviceId,
                z: z,
            }).then(() => {
            });
        });
    }

    /**
     * 通知机械臂抬起（触控笔抬起）
     * @param websocketService
     * @param deviceId
     */
    static notifyRelease(websocketService: WebsocketService, deviceId: string): Promise<any> {
        return new Promise((resolve) => {
            websocketService.on('notify-robot-arm-release-return', res => {
                if (deviceId === res.json.deviceId) {
                    resolve(res.json.status);
                }
            });

            websocketService.send('notify-robot-arm-release', {
                deviceId: deviceId,
            }).then(() => {
            });
        });
    }

    /**
     * 通知机械臂点击（移动到坐标后按下再抬起）
     * @param websocketService
     * @param deviceId
     * @param x
     * @param y
     */
    static notifyClick(websocketService: WebsocketService, deviceId: string, x: number, y: number): Promise<any> {
        return new Promise((resolve) => {
            websocketService.on('notify-robot-arm-click-return', res => {
                if (deviceId === res.json.deviceId) {
                    resolve(res.json.status);
                }
            });

            websocketService.send('notify-robot-arm-click', {
                deviceId: deviceId,
                x: x,
                y: y,
            }).then(() => {
            });
        });
    }

    /**
     * 通知机械臂滑动（从起点滑到终点）
     * @param websocketService
     * @param deviceId
     * @param x1
     * @param y1
     * @param x2
     * @param y2
     */
    static notifySwipe(
        websocketService: WebsocketService,
        deviceId: string,
        x1: number, y1: number, x2: number, y2: number
    ): Promise<any> {
        return new Promise((resolve) => {
            websocketService.on('notify-robot-arm-swipe-return', res => {
                if (deviceId === res.json.deviceId) {
                    resolve(res.json.status);
                }
            });

            websocketService.send('notify-robot-arm-swipe', {
                deviceId: deviceId,
                x1: x1,
                y1: y1,
                x2: x2,
                y2: y2,
            }).then(() => {
            });
        });
    }


}