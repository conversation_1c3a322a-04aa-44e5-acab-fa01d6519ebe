"""
图像处理工具模块
提供统一的图像加载、旋转、坐标转换等功能
"""

import cv2
import numpy as np
from fastapi import UploadFile, HTTPException
from typing import Tuple, List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class ImageProcessor:
    """图像处理器"""
    
    @staticmethod
    async def load_image_from_upload(file: UploadFile) -> np.ndarray:
        """从上传文件加载图像"""
        try:
            contents = await file.read()
            nparr = np.frombuffer(contents, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            if image is None:
                raise ValueError("无法解码图像文件")
            return image
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"图像加载失败: {str(e)}")
    
    @staticmethod
    def rotate_image(image: np.ndarray, angle: int) -> np.ndarray:
        """
        旋转图像
        
        Args:
            image: 输入图像
            angle: 旋转角度 (90, 180, 270 或 -90, -180, -270)
        
        Returns:
            旋转后的图像
        """
        if angle == 0:
            return image
        
        if angle == 90 or angle == -270:
            # 顺时针旋转90度
            return cv2.rotate(image, cv2.ROTATE_90_CLOCKWISE)
        elif angle == 180 or angle == -180:
            # 旋转180度
            return cv2.rotate(image, cv2.ROTATE_180)
        elif angle == 270 or angle == -90:
            # 逆时针旋转90度（顺时针旋转270度）
            return cv2.rotate(image, cv2.ROTATE_90_COUNTERCLOCKWISE)
        else:
            # 对于其他角度，使用仿射变换
            h, w = image.shape[:2]
            center = (w // 2, h // 2)
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            
            # 计算旋转后的图像尺寸
            cos_val = abs(rotation_matrix[0, 0])
            sin_val = abs(rotation_matrix[0, 1])
            new_w = int((h * sin_val) + (w * cos_val))
            new_h = int((h * cos_val) + (w * sin_val))
            
            # 调整旋转中心
            rotation_matrix[0, 2] += (new_w / 2) - center[0]
            rotation_matrix[1, 2] += (new_h / 2) - center[1]
            
            return cv2.warpAffine(image, rotation_matrix, (new_w, new_h))

class CoordinateConverter:
    """坐标转换器"""
    
    @staticmethod
    def yolo_to_ocr_coordinates(
        yolo_bbox: List[float], 
        original_size: Tuple[int, int], 
        rotated_size: Tuple[int, int]
    ) -> Dict[str, Any]:
        """
        将YOLO坐标转换为OCR坐标系
        
        Args:
            yolo_bbox: YOLO边界框 [x_min, y_min, x_max, y_max] (原图坐标)
            original_size: 原图尺寸 (width, height) - 720x1280
            rotated_size: 旋转后尺寸 (width, height) - 1280x720
            
        Returns:
            OCR格式的边界框信息
        """
        x_min, y_min, x_max, y_max = yolo_bbox
        orig_w, orig_h = original_size
        rot_w, rot_h = rotated_size
        
        # YOLO使用原图(720x1280)，OCR使用旋转90度后的图(1280x720)
        # 原图坐标 -> 旋转90度后坐标的转换公式:
        # 新x = 原y
        # 新y = 原图宽度 - 原x
        
        # 转换四个角点
        # 原图的四个角点
        points_orig = [
            [x_min, y_min],  # 左上
            [x_max, y_min],  # 右上
            [x_max, y_max],  # 右下
            [x_min, y_max]   # 左下
        ]
        
        # 转换到旋转后的坐标系
        points_rotated = []
        for x, y in points_orig:
            new_x = y  # 原y变成新x
            new_y = orig_w - x  # 原图宽度减去原x变成新y
            points_rotated.append([new_x, new_y])
        
        # 计算旋转后坐标系中的边界框
        x_coords = [point[0] for point in points_rotated]
        y_coords = [point[1] for point in points_rotated]
        
        rot_x_min = int(min(x_coords))
        rot_y_min = int(min(y_coords))
        rot_x_max = int(max(x_coords))
        rot_y_max = int(max(y_coords))
        
        return {
            "points": points_rotated,  # 四个角点坐标
            "rectangle": {
                "x_min": rot_x_min,
                "y_min": rot_y_min,
                "x_max": rot_x_max,
                "y_max": rot_y_max,
                "width": rot_x_max - rot_x_min,
                "height": rot_y_max - rot_y_min,
                "center_x": int((rot_x_max + rot_x_min) / 2),
                "center_y": int((rot_y_max + rot_y_min) / 2)
            },
            "top_left": [rot_x_min, rot_y_min],
            "top_right": [rot_x_max, rot_y_min],
            "bottom_left": [rot_x_min, rot_y_max],
            "bottom_right": [rot_x_max, rot_y_max],
            "opencv_rect": [rot_x_min, rot_y_min, rot_x_max - rot_x_min, rot_y_max - rot_y_min]
        }
    
    @staticmethod
    def format_ocr_bounding_box(box: List[List[float]]) -> Dict[str, Any]:
        """
        格式化OCR边界框信息
        
        Args:
            box: PaddleOCR返回的四个角点坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
        
        Returns:
            dict: 包含多种格式的边界框信息
        """
        # 提取所有x和y坐标
        x_coords = [point[0] for point in box]
        y_coords = [point[1] for point in box]
        
        x_min = int(min(x_coords))
        y_min = int(min(y_coords))
        x_max = int(max(x_coords))
        y_max = int(max(y_coords))
        
        return {
            "points": box,  # 原始四个角点坐标
            "rectangle": {
                "x_min": x_min,
                "y_min": y_min,
                "x_max": x_max,
                "y_max": y_max,
                "width": x_max - x_min,
                "height": y_max - y_min,
                "center_x": int((x_max + x_min) / 2),
                "center_y": int((y_max + y_min) / 2)
            },
            "top_left": [x_min, y_min],
            "top_right": [x_max, y_min],
            "bottom_left": [x_min, y_max],
            "bottom_right": [x_max, y_max],
            "opencv_rect": [x_min, y_min, x_max - x_min, y_max - y_min]
        }

class FileUploadHandler:
    """文件上传处理器"""
    
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    ALLOWED_CONTENT_TYPES = ['image/jpeg', 'image/png', 'image/bmp', 'image/tiff', 'image/webp']
    
    @classmethod
    def validate_upload_file(cls, file: UploadFile) -> None:
        """验证上传文件"""
        if file.size and file.size > cls.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=413, 
                detail=f"文件大小不能超过 {cls.MAX_FILE_SIZE / 1024 / 1024}MB"
            )
        
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="请上传图像文件")
    
    @classmethod
    async def process_upload_file(cls, file: UploadFile) -> Tuple[np.ndarray, bytes]:
        """
        处理上传文件
        
        Returns:
            Tuple[np.ndarray, bytes]: (图像数组, 原始字节数据)
        """
        cls.validate_upload_file(file)
        
        # 读取文件内容
        contents = await file.read()
        
        # 解码图像
        nparr = np.frombuffer(contents, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if image is None:
            raise HTTPException(status_code=400, detail="无法解码图像文件")
        
        return image, contents
