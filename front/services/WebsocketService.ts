import config from "../config";

type ConnectionStatus = 'disconnected' | 'connecting' | 'connected';
type EventCallback = (data: any) => void;
type MessageItem = { eventName: string; data: any; timestamp: number };

/**
 * 优化的WebSocket服务类
 * 功能：自动重连、心跳保活、消息队列、事件管理
 */
export class WebsocketService {
    // 连接相关
    private websocket: WebSocket | null = null;
    private deviceId = '';
    public connectionStatus: ConnectionStatus = 'disconnected';
    private isManualClose = false;
    private isConnecting = false;

    // 事件管理
    private eventListeners = new Map<string, Set<EventCallback>>();

    // 重连配置
    private reconnectAttempts = 0;
    private readonly maxReconnectAttempts = 10;
    private readonly baseReconnectDelay = 1000; // 基础延迟1秒
    private readonly maxReconnectDelay = 30000; // 最大延迟30秒
    private reconnectTimer: number | null = null;

    // 心跳配置
    private heartbeatTimer: number | null = null;
    private heartbeatTimeoutTimer: number | null = null;
    private readonly heartbeatInterval = 30000; // 30秒心跳
    private readonly heartbeatTimeout = 10000; // 10秒超时
    private heartbeatFailures = 0;
    private readonly maxHeartbeatFailures = 3;

    // 消息队列
    private messageQueue: MessageItem[] = [];
    private readonly maxQueueSize = 50;
    private readonly messageExpireTime = 30000; // 消息过期时间30秒

    constructor() {
        // 所有属性已在类定义时初始化
    }

    // ==================== 公共 API ====================

    /**
     * 初始化连接
     */
    async init(deviceId: string): Promise<void> {
        if (!deviceId?.trim()) {
            throw new Error('设备ID不能为空');
        }

        this.deviceId = deviceId.trim();
        this.isManualClose = false;
        await this.connect();
    }

    /**
     * 添加事件监听器
     */
    on(eventType: string, callback: EventCallback): void {
        if (!eventType || typeof callback !== 'function') {
            throw new Error('事件类型和回调函数都是必需的');
        }

        if (!this.eventListeners.has(eventType)) {
            this.eventListeners.set(eventType, new Set());
        }
        this.eventListeners.get(eventType)!.add(callback);
    }

    /**
     * 移除事件监听器
     */
    off(eventType: string, callback?: EventCallback): void {
        const listeners = this.eventListeners.get(eventType);
        if (!listeners) return;

        if (callback) {
            listeners.delete(callback);
        } else {
            listeners.clear();
        }

        if (listeners.size === 0) {
            this.eventListeners.delete(eventType);
        }
    }

    /**
     * 发送消息
     */
    async send(eventName: string, data: any = {}, binaryData: any) {
        if (!eventName?.trim()) {
            throw new Error('事件名称不能为空');
        }

        // 连接不可用时加入队列
        if (!this.isConnected()) {
            this.queueMessage(eventName, data);
            return false;
        }

        return await this.sendMessage(eventName, data, binaryData);
    }

    /**
     * 手动关闭连接
     */
    close(): void {
        this.isManualClose = true;
        this.cleanup();
        console.log('WebSocket 连接已手动关闭');
    }

    // ==================== 私有方法 ====================

    /**
     * 建立连接
     */
    private async connect(): Promise<void> {
        if (this.isConnecting) {
            console.log('连接进行中，请稍候...');
            return;
        }

        if (this.websocket?.readyState === WebSocket.OPEN) {
            return;
        }

        this.isConnecting = true;
        this.connectionStatus = 'connecting';
        this.emit('connecting');

        try {
            await this.createConnection();
        } catch (error) {
            console.error('连接失败:', error);
            this.connectionStatus = 'disconnected';
            this.isConnecting = false;
            this.emit('error', error);
            this.scheduleReconnect();
            throw error;
        }
    }

    /**
     * 创建 WebSocket 连接
     */
    private async createConnection(): Promise<void> {
        return new Promise((resolve, reject) => {
            const protocol = config.useSSL ? 'wss' : 'ws';
            const wsUrl = `${protocol}://${config.host}:${config.port}/ws/${this.deviceId}`;
            console.log('连接 WebSocket:', wsUrl);

            this.cleanupConnection();
            this.websocket = new WebSocket(wsUrl);

            // 连接超时
            const timeout = setTimeout(() => {
                if (this.websocket?.readyState === WebSocket.CONNECTING) {
                    this.websocket.close();
                    reject(new Error('连接超时'));
                }
            }, 10000);

            this.websocket.onopen = () => {
                clearTimeout(timeout);
                this.isConnecting = false;
                this.connectionStatus = 'connected';
                this.reconnectAttempts = 0;
                this.heartbeatFailures = 0;

                console.log('WebSocket 连接成功');
                this.startHeartbeat();
                this.processMessageQueue();
                this.emit('connected', {deviceId: this.deviceId});
                resolve();
            };

            this.websocket.onmessage = (event) => {
                this.handleMessage(event);
            };

            this.websocket.onclose = (event) => {
                clearTimeout(timeout);
                this.connectionStatus = 'disconnected';
                this.stopHeartbeat();

                console.log('WebSocket 连接关闭:', event.code, event.reason);
                this.emit('disconnected', {code: event.code, reason: event.reason});

                if (!this.isManualClose) {
                    this.scheduleReconnect();
                }

                if (event.code !== 1000) {
                    reject(new Error(`连接关闭: ${event.reason || event.code}`));
                }
            };

            this.websocket.onerror = (error) => {
                clearTimeout(timeout);
                console.error('WebSocket 错误:', error);
                this.emit('error', error);
                reject(new Error('WebSocket 连接失败'));
            };
        });
    }

    /**
     * 处理收到的消息
     */
    private async handleMessage(data: any): Promise<void> {
        try {
            const result = await this.parseMessage(data);
            // 处理心跳消息
            if (result.json.eventName === 'pong') {
                this.handleHeartbeatResponse(result.json.timestamp);
                return;
            }

            if (result.json.eventName === 'ping') {
                this.sendMessage('pong', {timestamp: result.json.timestamp}).catch(error => {
                    console.warn('响应 ping 失败:', error);
                });
                return;
            }

            this.emit(result.json.eventName, result);
        } catch (error) {
            console.error('处理消息失败:', error);
        }
    }

    /**
     * 解析接收到的 WebSocket 消息
     * @returns {Promise<{json: object, binary: ArrayBuffer|null}>}
     */
    /**
     * 解析 WebSocket 消息（直接操作 Blob，零拷贝优化）
     * @param {MessageEvent<Blob>} event
     * @returns {Promise<{ json: object, blob: Blob | null }>}
     */
    private async parseMessage(event: any): Promise<{ json: any; blob: Blob | null; }> {
        // 1. 直接利用 Blob 的 stream() API 避免内存复制
        const blob = event.data;
        if (blob.size === 0) return {json: {}, blob: null};

        // 2. 读取前4字节获取头部长度（使用流式读取）
        const headerLengthBuffer = await blob.slice(0, 4).arrayBuffer();
        const headerLength = new DataView(headerLengthBuffer).getUint32(0, false);

        // 3. 读取头部JSON（流式读取，避免加载整个Blob）
        const headerBlob = blob.slice(4, 4 + headerLength);
        const headerText = await headerBlob.text();
        const json = JSON.parse(headerText);

        // 4. 直接返回原始Blob的剩余部分（零拷贝）
        const binaryStart = 4 + headerLength;
        const payloadBlob = binaryStart < blob.size
            ? blob.slice(binaryStart)
            : null;

        return {json, blob: payloadBlob};
    }


    /**
     * 安排重连
     */
    private scheduleReconnect(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('达到最大重连次数，停止重连');
            this.emit('reconnect_failed', {attempts: this.reconnectAttempts});
            return;
        }

        this.reconnectAttempts++;

        // 指数退避：1s, 2s, 4s, 8s, 16s, 30s(max)
        const delay = Math.min(
            this.baseReconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
            this.maxReconnectDelay
        );

        console.log(`${(delay / 1000).toFixed(1)}秒后第${this.reconnectAttempts}次重连`);

        this.reconnectTimer = setTimeout(() => {
            if (this.connectionStatus === 'disconnected' && !this.isManualClose && !this.isConnecting) {
                this.emit('reconnecting', {attempt: this.reconnectAttempts});
                this.connect().catch(error => {
                    console.error('重连失败:', error);
                });
            }
        }, delay);
    }

    /**
     * 启动心跳
     */
    private startHeartbeat(): void {
        this.stopHeartbeat();

        this.heartbeatTimer = setInterval(() => {
            this.sendHeartbeat();
        }, this.heartbeatInterval);

        console.log('心跳已启动');
    }

    /**
     * 停止心跳
     */
    private stopHeartbeat(): void {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }

        if (this.heartbeatTimeoutTimer) {
            clearTimeout(this.heartbeatTimeoutTimer);
            this.heartbeatTimeoutTimer = null;
        }
    }

    /**
     * 发送心跳
     */
    private sendHeartbeat(): void {
        if (!this.isConnected()) return;

        const timestamp = Date.now();

        this.sendMessage('ping', {timestamp}).catch(error => {
            console.warn('心跳发送失败:', error);
            this.heartbeatFailures++;
        });

        // 心跳超时检测
        this.heartbeatTimeoutTimer = setTimeout(() => {
            console.warn('心跳超时');
            this.heartbeatFailures++;

            if (this.heartbeatFailures >= this.maxHeartbeatFailures) {
                console.error('心跳连续失败，主动断开连接');
                this.websocket?.close();
            }
        }, this.heartbeatTimeout);
    }

    /**
     * 处理心跳响应
     */
    private handleHeartbeatResponse(timestamp: number): void {
        if (this.heartbeatTimeoutTimer) {
            clearTimeout(this.heartbeatTimeoutTimer);
            this.heartbeatTimeoutTimer = null;
        }

        this.heartbeatFailures = 0;

        // 计算延迟
        const latency = Date.now() - timestamp;
        if (latency > 1000) {
            console.warn(`心跳延迟: ${latency}ms`);
        }
    }

    /**
     * 检查连接状态
     */
    private isConnected(): boolean {
        return this.websocket?.readyState === WebSocket.OPEN && this.connectionStatus === 'connected';
    }

    /**
     * 消息入队
     */
    private queueMessage(eventName: string, data: any): void {
        if (this.messageQueue.length >= this.maxQueueSize) {
            console.warn('消息队列已满，丢弃最旧消息');
            this.messageQueue.shift();
        }

        this.messageQueue.push({eventName, data, timestamp: Date.now()});
        console.log(`消息已入队: ${eventName}`);
    }

    /**
     * 处理消息队列
     */
    private async processMessageQueue(): Promise<void> {
        if (this.messageQueue.length === 0) return;

        // 清理过期消息
        const now = Date.now();
        const validMessages = this.messageQueue.filter(msg => now - msg.timestamp < this.messageExpireTime);
        const expiredCount = this.messageQueue.length - validMessages.length;

        if (expiredCount > 0) {
            console.log(`清理 ${expiredCount} 条过期消息`);
        }

        this.messageQueue = validMessages;

        // 发送队列消息
        while (this.messageQueue.length > 0 && this.isConnected()) {
            const message = this.messageQueue.shift()!;
            try {
                await this.sendMessage(message.eventName, message.data);
                await new Promise(resolve => setTimeout(resolve, 10)); // 防止发送过快
            } catch (error) {
                console.error('发送队列消息失败:', error);
                break;
            }
        }

        if (this.messageQueue.length > 0) {
            console.log(`队列中还有 ${this.messageQueue.length} 条消息待发送`);
        }
    }


    /**
     * 发送消息到服务器
     * @param {string} eventName - 消息类型
     * @param  jsonData - 消息对象
     * @param binaryData
     */
    private async sendMessage(eventName: string, jsonData: any = {}, binaryData: any = null) {
        // 1. 准备头部 JSON
        jsonData['eventName'] = eventName;
        jsonData['sendDeviceId'] = this.deviceId;
        const headerStr = JSON.stringify(jsonData);
        const headerBuf = new TextEncoder().encode(headerStr);

        // 2. 准备二进制数据（如果有）
        let binaryBuf = new ArrayBuffer(0);
        if (binaryData instanceof Blob) {
            binaryBuf = await binaryData.arrayBuffer();
        } else if (binaryData instanceof ArrayBuffer) {
            binaryBuf = binaryData;
        }

        // 3. 构造完整消息 [4字节头长度][header][binaryData]
        const headerLength = headerBuf.length;
        const message = new Uint8Array(4 + headerLength + binaryBuf.byteLength);

        // 写入头长度（4字节，大端序）
        new DataView(message.buffer).setUint32(0, headerLength, false);

        // 写入头部 JSON
        message.set(headerBuf, 4);

        // 写入二进制数据（如果有）
        if (binaryBuf.byteLength > 0) {
            message.set(new Uint8Array(binaryBuf), 4 + headerLength);
        }

        // 4. 发送
        this.websocket?.send(message);
    }

    /**
     * 触发事件
     */
    private emit(eventType: string, data: any = {}): void {
        const listeners = this.eventListeners.get(eventType);
        if (!listeners || listeners.size === 0) return;

        // 使用数组避免迭代过程中修改集合的问题
        Array.from(listeners).forEach(callback => {
            try {
                callback(data);
            } catch (error) {
                console.error(`事件监听器错误 (${eventType}):`, error);
                // 移除出错的监听器
                listeners.delete(callback);
            }
        });
    }

    /**
     * 清理连接
     */
    private cleanupConnection(): void {
        this.stopHeartbeat();

        if (this.websocket) {
            // 清理事件处理器
            this.websocket.onopen = null;
            this.websocket.onmessage = null;
            this.websocket.onclose = null;
            this.websocket.onerror = null;

            // 关闭连接
            if (this.websocket.readyState === WebSocket.OPEN ||
                this.websocket.readyState === WebSocket.CONNECTING) {
                this.websocket.close();
            }

            this.websocket = null;
        }
    }

    /**
     * 清理所有资源
     */
    private cleanup(): void {
        this.cleanupConnection();

        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        this.connectionStatus = 'disconnected';
        this.reconnectAttempts = 0;
        this.isConnecting = false;
        this.heartbeatFailures = 0;
        this.messageQueue.length = 0;
        this.eventListeners.clear();
    }
}
