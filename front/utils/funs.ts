export function sleep(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
}


export function showModal(opt: object) {
    uni.showModal(opt)
}

export function showToast(opt: object) {
    uni.showToast(opt)
}


export function mobileLog(message:string, type:string = 'info') {
    // 使用uni-app的全局事件系统
    uni.$emit('taskRunner:debug', {message, type});
    // PC 直接显示日志
    console.log(message);
}
