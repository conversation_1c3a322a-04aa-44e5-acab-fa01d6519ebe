<template>
  <view class="connection-status">
    <text class="status-label">连接状态:</text>
    <text :class="['status-text', connectionStatus]">{{ getStatusText() }}</text>
  </view>
</template>

<script lang="ts">
import {WebsocketService} from "../../services/WebsocketService";
export default {
  name: 'ConnectionStatus',
  props: {
    websocketService: {
      type: WebsocketService,
      required: true
    }
  },
  computed: {
    connectionStatus() {
      return this.websocketService ? this.websocketService.connectionStatus : 'disconnected';
    }
  },
  methods: {
    getStatusText() {
      switch (this.connectionStatus) {
        case 'disconnected':
          return '未连接';
        case 'connecting':
          return '连接中...';
        case 'connected':
          return '已连接';
        default:
          return '未知状态';
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.connection-status {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  background-color: white;
  padding: 12px;
  border-radius: 5px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.status-label {
  font-size: 14px;
  color: #666;
  margin-right: 5px;
}

.status-text {
  font-size: 14px;
  font-weight: bold;

  &.connected {
    color: #4CAF50;
  }

  &.connecting {
    color: #FF9800;
  }

  &.disconnected {
    color: #F44336;
  }
}
</style>