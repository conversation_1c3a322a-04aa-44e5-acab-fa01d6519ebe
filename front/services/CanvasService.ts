interface ClickMarker {
    x: number;
    y: number;
    canvasX: number;
    canvasY: number;
}

interface CanvasSize {
    width: number;
    height: number;
}

/**
 * Canvas服务类
 * 封装canvas相关的绘制和交互逻辑
 */
export class CanvasService {
    private readonly canvasId: string;
    private readonly component: any;
    private canvasWidth: number = 0;
    private canvasHeight: number = 0;
    private imageWidth: number = 0;
    private imageHeight: number = 0;
    private clickMarkers: ClickMarker[] = [];

    constructor(canvasId: string, component: any) {
        this.canvasId = canvasId;
        this.component = component;
        this.canvasWidth = 0;
        this.canvasHeight = 0;
        this.imageWidth = 0;
        this.imageHeight = 0;
        this.clickMarkers = [];
    }

    /**
     * 在canvas上绘制照片和标记
     * @param {string} photoData - 照片数据
     * @param {Function} callback - 绘制完成后的回调函数
     */
    drawPhotoOnCanvas(photoData: string, callback?: (size: CanvasSize) => void): void {
        try {
            if (!photoData) {
                console.log('没有照片数据');
                return;
            }

            const img = new Image();
            img.onload = () => {
                const originalWidth = img.width;
                const originalHeight = img.height;

                this.canvasWidth = originalHeight;
                this.canvasHeight = originalWidth;
                this.imageWidth = originalWidth;
                this.imageHeight = originalHeight;

                this.component.$nextTick(() => {
                    const ctx = uni.createCanvasContext(this.canvasId, this.component);

                    ctx.translate(this.canvasWidth / 2, this.canvasHeight / 2);
                    ctx.rotate(Math.PI / 2);
                    ctx.drawImage(photoData, -this.canvasHeight / 2, -this.canvasWidth / 2, this.canvasHeight, this.canvasWidth);

                    // 恢复变换以便绘制标记
                    ctx.rotate(-Math.PI / 2);
                    ctx.translate(-this.canvasWidth / 2, -this.canvasHeight / 2);

                    // 绘制标记点
                    this.clickMarkers.forEach((marker, index) => {
                        // 绘制红色圆圈
                        ctx.beginPath();
                        ctx.arc(marker.canvasX, marker.canvasY, 8, 0, 2 * Math.PI);
                        ctx.fillStyle = 'red';
                        ctx.fill();

                        // 在圆圈中心绘制白色编号
                        ctx.font = '12px Arial';
                        ctx.fillStyle = 'white';
                        ctx.setTextAlign('center');
                        ctx.setTextBaseline('middle') ;
                        ctx.fillText((index + 1).toString(), marker.canvasX, marker.canvasY);
                    });

                    ctx.draw();
                    
                    // 绘制完成后执行回调
                    if (callback) {
                        callback({
                            width: this.canvasWidth,
                            height: this.canvasHeight
                        });
                    }
                });
            };

            img.onerror = () => {
                console.error('图片加载失败');
                uni.showToast({title: '照片显示失败', icon: 'error'});
            };

            img.src = photoData;

        } catch (error) {
            console.error('绘制照片到canvas失败:', error);
            uni.showToast({title: '照片显示失败', icon: 'error'});
        }
    }

    /**
     * 处理canvas点击事件
     * @param {Object} e - 点击事件对象
     * @param {Function} callback - 点击处理完成后的回调函数
     */
    handleCanvasTap(e: any, callback?: (markers: ClickMarker[]) => void): void {
        // 使用uni.createSelectorQuery获取canvas元素的准确位置
        const query = uni.createSelectorQuery().in(this.component);
        query.select(`#${this.canvasId}`).boundingClientRect();
        query.exec(res => {
            const canvasRect = res[0];
            if (canvasRect) {
                // 计算相对于canvas的准确坐标
                const canvasX = Math.round(e.detail.x - canvasRect.left);
                const canvasY = Math.round(e.detail.y - canvasRect.top);

                // 确保坐标在canvas范围内
                const validX = Math.max(0, Math.min(this.canvasWidth, canvasX));
                const validY = Math.max(0, Math.min(this.canvasHeight, canvasY));

                // 由于图片旋转了90度，需要将canvas坐标转换为原始图片坐标
                const imageX = Math.round(validY);
                const imageY = Math.round(validX);

                // 添加点击标记
                this.clickMarkers = [{
                    x: imageX,
                    y: imageY,
                    canvasX: validX,
                    canvasY: validY
                }];

                console.log(this.clickMarkers);
                
                // 执行回调
                if (callback) {
                    callback(this.clickMarkers);
                }
            }
        });
    }

    /**
     * 获取canvas尺寸
     * @returns {Object} 包含width和height的对象
     */
    getCanvasSize(): CanvasSize {
        return {
            width: this.canvasWidth,
            height: this.canvasHeight
        };
    }

    /**
     * 获取点击标记
     * @returns {Array} 点击标记数组
     */
    getClickMarkers(): ClickMarker[] {
        return this.clickMarkers;
    }

    /**
     * 清除点击标记
     */
    clearClickMarkers(): void {
        this.clickMarkers = [];
    }

    /**
     * 设置点击标记
     * @param {Array} markers - 标记数组
     */
    setClickMarkers(markers: ClickMarker[]): void {
        this.clickMarkers = markers;
    }
}