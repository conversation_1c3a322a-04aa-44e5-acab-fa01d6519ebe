<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI识别服务测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        .buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .preview {
            max-width: 100%;
            max-height: 300px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .detection-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 10px 0;
            border-radius: 4px;
            background: #f8f9fa;
        }
        .detection-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .detection-type {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .type-ocr {
            background: #e3f2fd;
            color: #1976d2;
        }
        .type-yolo {
            background: #f3e5f5;
            color: #7b1fa2;
        }
        .confidence {
            color: #666;
            font-size: 0.9em;
        }
        .coordinates {
            font-family: monospace;
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 AI识别服务测试</h1>
        <p style="text-align: center; color: #666;">
            统一识别服务 - 同时执行OCR文字识别和YOLO目标检测<br>
            支持720x1280图像，统一返回OCR坐标系结果
        </p>
        
        <div class="upload-area" id="uploadArea">
            <p>📁 点击选择图片或拖拽图片到此处</p>
            <p style="color: #666; font-size: 0.9em;">推荐尺寸: 720x1280 | 支持 JPG, PNG, BMP, TIFF, WebP 格式</p>
            <input type="file" id="fileInput" accept="image/*" style="display: none;">
        </div>
        
        <div class="buttons">
            <button id="recognizeBtn" disabled>🚀 开始统一识别</button>
            <button id="healthBtn">🏥 检查服务状态</button>
            <button id="infoBtn">ℹ️ 服务信息</button>
        </div>
        
        <div id="result"></div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const recognizeBtn = document.getElementById('recognizeBtn');
        const healthBtn = document.getElementById('healthBtn');
        const infoBtn = document.getElementById('infoBtn');
        const resultDiv = document.getElementById('result');
        
        let selectedFile = null;
        
        // 上传区域事件
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });
        
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });
        
        function handleFileSelect(file) {
            if (!file.type.startsWith('image/')) {
                showResult('请选择图像文件', 'error');
                return;
            }
            
            selectedFile = file;
            recognizeBtn.disabled = false;
            
            // 显示预览
            const reader = new FileReader();
            reader.onload = (e) => {
                const preview = document.createElement('img');
                preview.src = e.target.result;
                preview.className = 'preview';
                
                uploadArea.innerHTML = '';
                uploadArea.appendChild(preview);
                
                const info = document.createElement('p');
                info.textContent = `已选择: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
                info.style.marginTop = '10px';
                uploadArea.appendChild(info);
            };
            reader.readAsDataURL(file);
        }
        
        // 统一识别
        recognizeBtn.addEventListener('click', async () => {
            if (!selectedFile) return;
            
            recognizeBtn.disabled = true;
            showResult('正在执行统一识别...', 'loading');
            
            try {
                const formData = new FormData();
                formData.append('file', selectedFile);
                
                const response = await fetch('/unified/recognize', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    displayUnifiedResult(result);
                } else {
                    showResult(`识别失败: ${result.detail || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(`网络错误: ${error.message}`, 'error');
            } finally {
                recognizeBtn.disabled = false;
            }
        });
        
        // 健康检查
        healthBtn.addEventListener('click', async () => {
            try {
                const response = await fetch('/unified/health');
                const result = await response.json();
                
                if (response.ok) {
                    displayHealthStatus(result);
                } else {
                    showResult(`健康检查失败: ${result.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(`无法连接到服务: ${error.message}`, 'error');
            }
        });
        
        // 服务信息
        infoBtn.addEventListener('click', async () => {
            try {
                const response = await fetch('/unified/info');
                const result = await response.json();
                
                if (response.ok) {
                    displayServiceInfo(result);
                } else {
                    showResult(`获取服务信息失败: ${result.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(`无法连接到服务: ${error.message}`, 'error');
            }
        });
        
        function showResult(message, type) {
            resultDiv.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
        
        function displayUnifiedResult(result) {
            let html = '<div class="result success">';
            html += `<h3>✅ 统一识别成功</h3>`;
            
            // 统计信息
            html += '<div class="stats">';
            html += `<div class="stat-card">
                <div class="stat-number">${result.total_detections}</div>
                <div class="stat-label">总检测数</div>
            </div>`;
            html += `<div class="stat-card">
                <div class="stat-number">${result.ocr_count}</div>
                <div class="stat-label">OCR文字</div>
            </div>`;
            html += `<div class="stat-card">
                <div class="stat-number">${result.yolo_count}</div>
                <div class="stat-label">YOLO目标</div>
            </div>`;
            html += '</div>';
            
            // 图像信息
            if (result.image_info) {
                html += `<p><strong>📐 图像尺寸:</strong> ${result.image_info.original_size.width}x${result.image_info.original_size.height}</p>`;
                html += `<p><strong>🔄 坐标系:</strong> ${result.image_info.coordinate_system}</p>`;
            }
            
            // 检测结果
            if (result.detections && result.detections.length > 0) {
                html += '<h4>🎯 检测结果:</h4>';
                result.detections.forEach((detection, index) => {
                    const typeClass = detection.type === 'ocr' ? 'type-ocr' : 'type-yolo';
                    const bbox = detection.bounding_box.rectangle;
                    
                    html += `<div class="detection-item">`;
                    html += `<div class="detection-header">`;
                    html += `<strong>${index + 1}. ${detection.name}</strong>`;
                    html += `<span class="detection-type ${typeClass}">${detection.type.toUpperCase()}</span>`;
                    html += `</div>`;
                    html += `<div class="confidence">置信度: ${(detection.confidence * 100).toFixed(1)}%</div>`;
                    html += `<div class="coordinates">`;
                    html += `中心点: (${detection.center_x}, ${detection.center_y}) | `;
                    html += `边界框: (${bbox.x_min}, ${bbox.y_min}) → (${bbox.x_max}, ${bbox.y_max}) | `;
                    html += `尺寸: ${bbox.width}×${bbox.height}`;
                    html += `</div>`;
                    html += `</div>`;
                });
            }
            
            // 处理信息
            if (result.processing_info) {
                html += '<h4>⚙️ 处理信息:</h4>';
                html += `<p>OCR成功: ${result.processing_info.ocr_success ? '✅' : '❌'}</p>`;
                html += `<p>YOLO成功: ${result.processing_info.yolo_success ? '✅' : '❌'}</p>`;
                html += `<p>OCR旋转: ${result.processing_info.ocr_rotation}°</p>`;
                html += `<p>坐标转换: ${result.processing_info.yolo_coordinate_converted ? '✅' : '❌'}</p>`;
            }
            
            html += '</div>';
            resultDiv.innerHTML = html;
        }
        
        function displayHealthStatus(status) {
            let html = '<div class="result success">';
            html += '<h3>🏥 服务健康状态</h3>';
            
            // 统一服务状态
            html += `<h4>🔧 统一服务</h4>`;
            html += `<p>状态: ${status.unified_service.status}</p>`;
            html += `<p>版本: ${status.unified_service.version}</p>`;
            html += `<p>线程池大小: ${status.unified_service.thread_pool_size}</p>`;
            
            // OCR服务状态
            html += `<h4>📝 OCR服务</h4>`;
            html += `<p>状态: ${status.ocr_service.status}</p>`;
            if (status.ocr_service.ocr_engine) {
                html += `<p>引擎: ${status.ocr_service.ocr_engine}</p>`;
                html += `<p>模式: ${status.ocr_service.mode}</p>`;
            }
            
            // YOLO服务状态
            html += `<h4>🎯 YOLO服务</h4>`;
            html += `<p>状态: ${status.yolo_service.status}</p>`;
            if (status.yolo_service.error) {
                html += `<p style="color: red;">错误: ${status.yolo_service.error}</p>`;
            }
            
            html += '</div>';
            resultDiv.innerHTML = html;
        }
        
        function displayServiceInfo(info) {
            let html = '<div class="result success">';
            html += '<h3>ℹ️ 服务信息</h3>';
            html += `<p><strong>服务:</strong> ${info.service}</p>`;
            html += `<p><strong>版本:</strong> ${info.version}</p>`;
            html += `<p><strong>描述:</strong> ${info.description}</p>`;
            html += `<p><strong>支持格式:</strong> ${info.supported_formats.join(', ')}</p>`;
            html += `<p><strong>最大文件大小:</strong> ${info.max_file_size}</p>`;
            
            html += '<h4>📐 图像要求</h4>';
            html += `<p>推荐尺寸: ${info.image_requirements.recommended_size}</p>`;
            html += `<p>方向: ${info.image_requirements.orientation}</p>`;
            html += `<p>OCR处理: ${info.image_requirements.processing.ocr}</p>`;
            html += `<p>YOLO处理: ${info.image_requirements.processing.yolo}</p>`;
            
            html += '<h4>🔄 坐标系统</h4>';
            html += `<p>输出: ${info.coordinate_system.output}</p>`;
            html += `<p>转换: ${info.coordinate_system.conversion}</p>`;
            
            html += '</div>';
            resultDiv.innerHTML = html;
        }
        
        // 页面加载时检查服务状态
        window.addEventListener('load', () => {
            healthBtn.click();
        });
    </script>
</body>
</html>
