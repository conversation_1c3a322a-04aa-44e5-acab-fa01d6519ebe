/**
 * 机械臂服务类
 * 提供机械臂控制的静态方法
 */
export class TakePhoneSocket {

    /**
     * 手机发送照片到PC
     * @param websocketService
     * @param photoData
     * @param deviceId
     */
    static sendPhoneToPC(websocketService: any, photoData: any, deviceId: string): void {
        websocketService.send('take-photo-result', {
            deviceId: deviceId,
        }, photoData);
    }

    /**
     * PC 接收照片
     * @param websocketService
     * @param callback
     */
    static receivePhoneByMobile(websocketService: any, callback: (blob: any, deviceId: string) => void): void {
        websocketService.on('take-photo-result', (message: any) => {
            callback(message.blob, message.json.deviceId)
        });
    }

    /**
     * 通知手机拍照
     * @param websocketService
     * @param mobileId
     */
    static sendTakePhone(websocketService: any, mobileId: string): void {
        websocketService.send('take-photo', {
            deviceId: mobileId
        });
    }

    /**
     * 手机监听拍照指令
     * @param websocketService
     * @param deviceId
     * @param callback
     */
    static onTakePhone(websocketService: any, deviceId: string, callback: () => void): void {
        websocketService.on('take-photo', (res: any) => {
            if (res.json.deviceId === deviceId) {
                callback();
            }
        });
    }
}