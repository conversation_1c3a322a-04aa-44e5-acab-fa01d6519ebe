"""
OCR识别服务
封装PaddleOCR的识别逻辑，提供标准化的接口
"""

import cv2
import numpy as np
import logging
from typing import List, Dict, Any, Optional, Tuple
from fastapi import HTTPException
from .image_utils import ImageProcessor, CoordinateConverter

logger = logging.getLogger(__name__)

class OCRService:
    """OCR识别服务"""
    
    def __init__(self):
        self.ocr = None
        self._init_ocr()
    
    def _init_ocr(self):
        """初始化OCR引擎"""
        try:
            from paddleocr import PaddleOCR
            # 使用CPU版本，支持中英文
            self.ocr = PaddleOCR(
                use_angle_cls=True,  # 启用文字方向分类
                lang='ch',           # 支持中文
                use_gpu=False,       # 使用CPU
                show_log=False       # 关闭详细日志
            )
            logger.info("PaddleOCR 初始化成功 (CPU模式)")
        except Exception as e:
            logger.error(f"PaddleOCR 初始化失败: {e}")
            self.ocr = None
    
    def get_ocr_instance(self):
        """获取OCR实例，如果未初始化则尝试初始化"""
        if self.ocr is None:
            self._init_ocr()
            if self.ocr is None:
                raise HTTPException(
                    status_code=500, 
                    detail="OCR引擎初始化失败，请检查PaddleOCR是否正确安装"
                )
        return self.ocr
    
    def recognize_text(
        self, 
        image: np.ndarray, 
        rotation_angle: int = 90
    ) -> Dict[str, Any]:
        """
        识别图像中的文字
        
        Args:
            image: 输入图像
            rotation_angle: 旋转角度，默认90度
            
        Returns:
            识别结果字典
        """
        try:
            # 获取原始图像尺寸
            original_height, original_width = image.shape[:2]
            
            # 图像旋转处理
            if rotation_angle != 0:
                processed_image = ImageProcessor.rotate_image(image, rotation_angle)
            else:
                processed_image = image
            
            # 获取处理后图像尺寸
            processed_height, processed_width = processed_image.shape[:2]
            
            # OCR识别
            ocr_instance = self.get_ocr_instance()
            result = ocr_instance.ocr(processed_image, cls=True)
            
            # 解析结果
            texts = []
            details = []
            
            if result and result[0]:
                for line in result[0]:
                    if line:
                        box, (text, confidence) = line
                        texts.append(text)
                        
                        # 构建详细信息，包含格式化的边界框
                        detail = {
                            "name": text,
                            "type": "ocr",
                            "confidence": float(confidence),
                            "bounding_box": CoordinateConverter.format_ocr_bounding_box(box)
                        }
                        details.append(detail)
            
            return {
                "success": True,
                "count": len(texts),
                "texts": texts,
                "details": details,
                "full_text": " ".join(texts),
                "rotation_applied": rotation_angle,
                "image_size": {
                    "original": {
                        "width": original_width,
                        "height": original_height
                    },
                    "processed": {
                        "width": processed_width,
                        "height": processed_height
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            raise HTTPException(status_code=500, detail=f"OCR识别失败: {str(e)}")
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 简单测试OCR是否正常
            ocr_instance = self.get_ocr_instance()
            test_image = np.ones((100, 100, 3), dtype=np.uint8) * 255
            ocr_instance.ocr(test_image)
            return {
                "status": "healthy", 
                "ocr_engine": "PaddleOCR", 
                "mode": "CPU"
            }
        except Exception as e:
            return {
                "status": "unhealthy", 
                "error": str(e)
            }
    
    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        return {
            "service": "PaddleOCR Service",
            "version": "1.0.0",
            "engine": "PaddleOCR",
            "mode": "CPU",
            "supported_languages": ["ch", "en"],
            "features": [
                "文字识别",
                "文字方向检测",
                "图像旋转 (0°, 90°, 180°, 270°)",
                "详细边界框信息"
            ],
            "bounding_box_formats": [
                "原始四角点坐标",
                "矩形边界框",
                "OpenCV格式",
                "角点坐标"
            ]
        }

# 全局OCR服务实例
ocr_service = OCRService()
