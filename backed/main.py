from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from robot_arm_api import router as robot_arm_router
from websocket_api import router as websocket_router
from unified_api import router as unified_router
import os

app = FastAPI(
    title="AI服务API",
    description="包含机械臂控制、WebSocket通信和统一识别(OCR+YOLO)的综合API服务",
    version="1.0.0"
)

# 添加CORS中间件支持跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(robot_arm_router)
app.include_router(websocket_router)
app.include_router(unified_router)

# 添加测试页面路由
@app.get("/test")
async def test_page():
    """统一识别测试页面"""
    return FileResponse("unified_test.html")

@app.get("/unified/test")
async def unified_test_page():
    """统一识别测试页面 (兼容路径)"""
    return FileResponse("unified_test.html")

@app.get("/")
async def root():
    """根路径重定向到API文档"""
    return {
        "message": "AI服务API",
        "docs": "/docs",
        "test_page": "/test",
        "unified_api": {
            "recognize": "/unified/recognize",
            "health": "/unified/health",
            "info": "/unified/info",
            "test": "/unified/test"
        },
        "compatibility": {
            "ocr": "/unified/ocr",
            "yolo": "/unified/yolo"
        }
    }
