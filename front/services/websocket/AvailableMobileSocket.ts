import {ConfigService} from "../ConfigService";

export class AvailableMobileSocket {

    /**
     * 监听获取在线设备
     * @param websocketService
     */
    static onGetAvailableMobiles(websocketService: any): void {
        websocketService.on('get_available_mobiles', () => {
            websocketService.send('available_mobile', {deviceId: ConfigService.getDeviceId()});
        });
    }

    /**
     * 发送请求，获取在线设备
     * @param websocketService
     */
    static getAvailableMobiles(websocketService: any) {
        return new Promise(resolve => {
            websocketService.on('available_mobile', (message: any) => {
                console.log('收到可用手机列表:', message);
                resolve(message.json.deviceId);
            });
            websocketService.send('get_available_mobiles');
        });
    }

}