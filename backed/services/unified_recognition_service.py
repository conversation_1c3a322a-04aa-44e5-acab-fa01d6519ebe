"""
统一识别服务
整合OCR和YOLO识别，提供统一的文件上传和异步识别接口
"""

import asyncio
import logging
from typing import List, Dict, Any, Tuple
from fastapi import UploadFile, HTTPException, BackgroundTasks
from concurrent.futures import ThreadPoolExecutor
import os

from .image_utils import FileUploadHandler, ImageProcessor
from .ocr_service import ocr_service
from .yolo_service import yolo_service

logger = logging.getLogger(__name__)

class UnifiedRecognitionService:
    """统一识别服务"""
    
    def __init__(self):
        # 动态设置线程池大小为CPU核心数
        self.executor = ThreadPoolExecutor(max_workers=os.cpu_count() or 2)
        logger.info(f"统一识别服务初始化完成，线程池大小: {self.executor._max_workers}")
    
    async def process_image_upload(
        self, 
        file: UploadFile, 
        background_tasks: BackgroundTasks
    ) -> Dict[str, Any]:
        """
        处理图像上传并执行统一识别
        
        Args:
            file: 上传的图像文件
            background_tasks: FastAPI后台任务
            
        Returns:
            统一的识别结果
        """
        try:
            # 1. 验证和处理上传文件
            image, image_bytes = await FileUploadHandler.process_upload_file(file)
            
            # 2. 获取图像尺寸信息
            original_height, original_width = image.shape[:2]
            original_size = (original_width, original_height)  # (720, 1280)
            
            logger.info(f"处理图像: {file.filename}, 尺寸: {original_width}x{original_height}")
            
            # 3. 异步并行执行OCR和YOLO识别
            loop = asyncio.get_event_loop()
            
            # 创建异步任务
            ocr_task = loop.run_in_executor(
                self.executor, 
                self._run_ocr_recognition, 
                image
            )
            
            yolo_task = loop.run_in_executor(
                self.executor, 
                self._run_yolo_recognition, 
                image_bytes, 
                original_size
            )
            
            # 等待两个任务完成
            ocr_result, yolo_result = await asyncio.gather(ocr_task, yolo_task)
            
            # 4. 合并识别结果
            unified_result = self._merge_recognition_results(
                ocr_result, 
                yolo_result, 
                original_size
            )
            
            # 5. 根据YOLO检测结果决定是否保存图像（后台任务）
            if yolo_service and len(yolo_result) <= 1:
                background_tasks.add_task(
                    self._save_image_background, 
                    image_bytes, 
                    file.filename
                )
            
            return unified_result
            
        except Exception as e:
            logger.error(f"统一识别处理失败: {e}")
            raise HTTPException(status_code=500, detail=f"识别处理失败: {str(e)}")
    
    def _run_ocr_recognition(self, image) -> Dict[str, Any]:
        """在线程池中运行OCR识别"""
        try:
            return ocr_service.recognize_text(image, rotation_angle=90)
        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "details": []
            }
    
    def _run_yolo_recognition(self, image_bytes: bytes, original_size: Tuple[int, int]) -> List[Dict[str, Any]]:
        """在线程池中运行YOLO识别"""
        try:
            if yolo_service is None:
                logger.warning("YOLO服务未初始化")
                return []
            
            return yolo_service.detect_objects(
                image_bytes, 
                original_size=original_size, 
                convert_to_ocr_coords=True
            )
        except Exception as e:
            logger.error(f"YOLO识别失败: {e}")
            return []
    
    def _merge_recognition_results(
        self, 
        ocr_result: Dict[str, Any], 
        yolo_result: List[Dict[str, Any]], 
        original_size: Tuple[int, int]
    ) -> Dict[str, Any]:
        """合并OCR和YOLO识别结果"""
        
        # 提取OCR识别的详细信息
        ocr_details = ocr_result.get("details", []) if ocr_result.get("success") else []
        
        # 合并所有识别结果
        all_detections = []
        
        # 添加OCR结果
        for ocr_item in ocr_details:
            all_detections.append({
                "name": ocr_item["name"],
                "type": "ocr",
                "confidence": ocr_item["confidence"],
                "center_x": ocr_item["bounding_box"]["rectangle"]["center_x"],
                "center_y": ocr_item["bounding_box"]["rectangle"]["center_y"],
                "bounding_box": ocr_item["bounding_box"]
            })
        
        # 添加YOLO结果
        for yolo_item in yolo_result:
            all_detections.append({
                "name": yolo_item["name"],
                "type": "yolo",
                "confidence": yolo_item["confidence"],
                "center_x": yolo_item["bounding_box"]["rectangle"]["center_x"],
                "center_y": yolo_item["bounding_box"]["rectangle"]["center_y"],
                "bounding_box": yolo_item["bounding_box"]
            })
        
        # 构建统一响应
        response = {
            "success": True,
            "total_detections": len(all_detections),
            "ocr_count": len(ocr_details),
            "yolo_count": len(yolo_result),
            "detections": all_detections,
            "image_info": {
                "original_size": {
                    "width": original_size[0],
                    "height": original_size[1]
                },
                "coordinate_system": "OCR坐标系 (旋转90度后)",
                "description": "所有坐标已统一转换为OCR坐标系"
            },
            "processing_info": {
                "ocr_success": ocr_result.get("success", False),
                "yolo_success": len(yolo_result) >= 0,
                "ocr_rotation": ocr_result.get("rotation_applied", 90),
                "yolo_coordinate_converted": True
            }
        }
        
        # 如果OCR识别失败，添加错误信息
        if not ocr_result.get("success"):
            response["ocr_error"] = ocr_result.get("error", "OCR识别失败")
        
        return response
    
    def _save_image_background(self, image_bytes: bytes, filename: str):
        """后台保存图像"""
        try:
            if yolo_service:
                yolo_service.save_image_if_needed(image_bytes, filename, 1)
        except Exception as e:
            logger.error(f"后台保存图像失败: {e}")
    
    def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        ocr_status = ocr_service.health_check()
        
        yolo_status = {
            "status": "healthy" if yolo_service else "unavailable",
            "error": "YOLO服务未初始化" if not yolo_service else None
        }
        
        return {
            "unified_service": {
                "status": "healthy",
                "version": "1.0.0",
                "thread_pool_size": self.executor._max_workers
            },
            "ocr_service": ocr_status,
            "yolo_service": yolo_status,
            "features": [
                "统一文件上传",
                "异步并行识别",
                "坐标系统一",
                "自动图像保存"
            ]
        }

# 全局统一识别服务实例
unified_recognition_service = UnifiedRecognitionService()
