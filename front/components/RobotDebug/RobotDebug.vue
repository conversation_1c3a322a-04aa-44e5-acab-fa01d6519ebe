<template>
  <view class="robot-debug-container">
    <!-- 端口控制组件 -->
    <PortControl :websocket-service="websocketService" :device-id="deviceId"/>
    <view class="section">
      <text>X：</text>
      <input v-model.number="x" type="number"/>
      <text>Y：</text>
      <input v-model.number="y" type="number"/>
      <view class="btn" @click="moveTo">移动</view>
      <view class="btn" @click="click">点击</view>
    </view>
    <view class="section">
      <text>Z：</text>
      <input v-model.number="z" type="number" placeholder="默认6"/>
      <view class="btn" @click="press">按下</view>
      <view class="btn" @click="release">抬起</view>
    </view>
    <view class="section">
      <text>滑动：</text>
      <text>起点</text>
      <input v-model.number="x1" type="number" placeholder="x1"/>
      <input v-model.number="y1" type="number" placeholder="y1"/>
      <text>终点</text>
      <input v-model.number="x2" type="number" placeholder="x2"/>
      <input v-model.number="y2" type="number" placeholder="y2"/>
      <view class="btn" @click="swipe">滑动</view>
    </view>
  </view>
</template>

<script lang="ts">
import PortControl from '../PortControl/PortControl';
import {RobotArmSocket} from "../../services/websocket/RobotArmSocket";

export default {
  components: {
    PortControl
  },
  props: {
    deviceId: {
      type: String,
      required: true
    },
    websocketService: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      x: 0,
      y: 0,
      z: 6,
      x1: 0,
      y1: 0,
      x2: 0,
      y2: 0,
      robotArmService: null,
    };
  },

  methods: {

    async moveTo() {
      await RobotArmSocket.notifyMoveTo(this.websocketService,this.deviceId,this.x, this.y);
    },
    async press() {
      await RobotArmSocket.notifyPress(this.websocketService,this.deviceId,this.z);
    },
    async release() {
      await RobotArmSocket.notifyRelease(this.websocketService,this.deviceId);
    },
    async click() {
      await RobotArmSocket.notifyClick(this.websocketService,this.deviceId,this.x, this.y);
    },
    async swipe() {
      await RobotArmSocket.notifySwipe(this.websocketService,this.deviceId,this.x1, this.y1, this.x2, this.y2);
    },

  },
};
</script>

<style scoped lang="scss">
.robot-debug-container {
  color: #333;
}

.section {
  background: #fff;
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 10px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

text {
  font-size: 12px;
  color: #555;
  line-height: 1.5;
}

.picker-view {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px 12px;
  background-color: #fff;
  min-width: 100px;
  text-align: center;
}

input {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 3px 12px;
  font-size: 12px;
  width: 50px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

input:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

</style>
